<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Créer un Concours - MEF</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        :root {
            --bleu-mef: #0056b3;
            --gris-fond: #f8f9fa;
        }

        body {
            background-color: var(--gris-fond);
        }

        .form-container {
            max-width: 900px;
            margin: 50px auto;
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        h2 {
            text-align: center;
            color: var(--bleu-mef);
            margin-bottom: 30px;
        }

        .form-label {
            font-weight: 500;
        }

        .btn-mef {
            background-color: var(--bleu-mef);
            color: white;
            font-weight: 600;
            padding: 12px;
            border-radius: 8px;
            border: none;
            margin-top: 20px;
        }

        .btn-mef:hover {
            background-color: #003f80;
        }

        .btn-home {
            background-color: var(--gris-fond);
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            font-weight: 500;
            margin-top: 15px;
            text-align: center;
            display: block;
            width: 100%;
            text-decoration: none;
            color: #000;
        }

        .btn-home:hover {
            background-color: #e9ecef;
            text-decoration: none;
        }

        .small-text {
            font-size: 0.8rem;
            color: #6c757d;
        }

        .date-info {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .invalid-feedback {
            display: none;
        }
    </style>
</head>
<body>

<div class="container form-container">
    <form id="concoursForm" method="POST" th:action="@{/admin/concours/save}" th:object="${concours}">
        <h2><i class="fas fa-calendar-alt me-2"></i>Création d'un Concours</h2>

        <!-- Messages d'erreur globaux -->
        <div th:if="${error}" class="alert alert-danger mb-3">
            <i class="fas fa-exclamation-circle me-2"></i>
            <span th:text="${error}">Erreur</span>
        </div>

        <!-- Message de succès -->
        <div th:if="${success}" class="alert alert-success mb-3">
            <i class="fas fa-check-circle me-2"></i>
            <span th:text="${success}">Succès</span>
        </div>

        <div class="row">
            <div class="col-md-12">
                <label class="form-label">Titre du concours :</label>
                <input type="text" name="titre" class="form-control" th:field="*{titre}" required>
                <div class="invalid-feedback" id="titre-error">
                    Le titre du concours est obligatoire (max 200 caractères)
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-4">
                <label class="form-label">Date d'ouverture :</label>
                <input type="date" id="dateOuverture" name="dateOuverture" class="form-control" th:field="*{dateOuverture}" required>
                <div class="date-info">Date à partir de laquelle les candidatures sont acceptées</div>
                <div class="invalid-feedback" id="dateOuverture-error">
                    La date d'ouverture est obligatoire et doit être aujourd'hui ou dans le futur
                </div>
            </div>
            <div class="col-md-4">
                <label class="form-label">Date de clôture :</label>
                <input type="date" id="dateCloture" name="dateCloture" class="form-control" th:field="*{dateCloture}" required>
                <div class="date-info">Date limite de dépôt des candidatures</div>
                <div class="invalid-feedback" id="dateCloture-error">
                    La date de clôture est obligatoire et doit être après la date d'ouverture
                </div>
            </div>
            <div class="col-md-4">
                <label class="form-label">Date du concours :</label>
                <input type="date" id="dateConcours" name="dateConcours" class="form-control" th:field="*{dateConcours}" required>
                <div class="date-info">Date à laquelle se déroulera le concours</div>
                <div class="invalid-feedback" id="dateConcours-error">
                    La date du concours est obligatoire et doit être après la date de clôture
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6">
                <label class="form-label">Nombre total de postes :</label>
                <input type="number" id="nbPostes" name="nbPostes" class="form-control" th:field="*{nbPostes}" min="1" required>
                <div class="invalid-feedback" id="nbPostes-error">
                    Le nombre de postes doit être au moins 1
                </div>
            </div>
            <div class="col-md-6">
                <label class="form-label">Statut :</label>
                <select class="form-select" name="publie" th:field="*{publie}">
                    <option value="false">Brouillon</option>
                    <option value="true">Publié</option>
                </select>
            </div>
        </div>

        <div class="mt-4">
            <label class="form-label">Spécialités concernées :</label>
            <div id="specialites-container">
                <div class="row mb-2">
                    <div class="col-md-8">
                        <select name="specialites[0].id" class="form-select" required>
                            <option value="">-- Choisir une spécialité --</option>
                            <option th:each="specialite : ${specialites}"
                                    th:value="${specialite.id}"
                                    th:text="${specialite.libelle}"></option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <input type="number" name="specialites[0].nbPostes" class="form-control" placeholder="Nombre de postes" min="1" required>
                    </div>
                </div>
            </div>
            <button type="button" id="ajouterSpecialite" class="btn btn-secondary btn-sm mt-2">
                <i class="fas fa-plus me-1"></i> Ajouter une spécialité
            </button>
        </div>

        <div class="mt-4">
            <label class="form-label">Centres d'examen :</label>
            <div class="row">
                <div class="col-md-12">
                    <select id="centresSelect" class="form-select" multiple size="5">
                        <option th:each="centre : ${centres}"
                                th:value="${centre.id}"
                                th:text="${centre.code} + ' - ' + ${centre.villeNom}">
                        </option>
                    </select>
                    <div class="invalid-feedback" id="centres-error">
                        Veuillez sélectionner au moins un centre d'examen
                    </div>
                </div>
            </div>

            <!-- Display selected centres -->
            <div class="mt-2 p-2 border rounded" id="centresSelectionnees">
                <p class="text-muted small-text mb-0">Aucun centre sélectionné</p>
            </div>

            <input type="hidden" name="centresExamenIds" id="centresExamenIds">
        </div>

        <div class="mt-4">
            <label class="form-label">Conditions de participation :</label>

            <!-- Conditions pré-définies -->
            <div class="mb-3">
                <label class="form-label small-text">Conditions pré-définies :</label>
                <div class="d-flex flex-wrap gap-2" id="conditions-predefinies">
                    <div th:each="condition,iter : ${conditionsPredefinies}" class="form-check form-check-inline">
                        <input class="form-check-input condition-checkbox" type="checkbox"
                               th:id="'cond-' + ${iter.index}"
                               th:value="${condition}">
                        <label class="form-check-label small-text"
                               th:for="'cond-' + ${iter.index}"
                               th:text="${condition}"></label>
                    </div>
                </div>
            </div>

            <!-- Condition personnalisée -->
            <div class="mb-3">
                <label class="form-label small-text">Ajouter une condition personnalisée :</label>
                <div class="input-group">
                    <input type="text" id="nouvelleCondition" class="form-control" placeholder="Saisissez votre condition">
                    <button type="button" id="ajouterCondition" class="btn btn-secondary">Ajouter</button>
                </div>
            </div>

            <!-- Affichage des conditions sélectionnées -->
            <div class="border p-2 mb-3" style="min-height: 100px;" id="conditionsSelectionnees">
                <p class="text-muted small-text mb-0">Aucune condition sélectionnée</p>
            </div>

            <!-- Champ caché pour les conditions -->
            <textarea name="conditions" class="d-none" id="conditionsInput" required></textarea>
            <div class="invalid-feedback" id="conditions-error">
                Au moins une condition doit être sélectionnée
            </div>
        </div>

        <button type="submit" class="btn btn-mef w-100 mt-4">
            <i class="fas fa-plus me-2"></i>Créer le concours
        </button>

        <a th:href="@{/admin/concours/list}" class="btn btn-home">
            <i class="fas fa-home me-2"></i>Retour à l'accueil
        </a>
    </form>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // Global variables
    let centresSelectionnes = [];
    let conditionsSelectionnees = [];
    let specialiteCounter = 1;

    document.addEventListener('DOMContentLoaded', function() {
        // 1. Gestion de l'ajout de spécialités
        document.getElementById('ajouterSpecialite').addEventListener('click', function() {
            const container = document.getElementById('specialites-container');
            const newRow = document.createElement('div');
            newRow.className = 'row mb-2';
            newRow.innerHTML = `
                <div class="col-md-8">
                    <select name="specialites[${specialiteCounter}].id" class="form-select" required>
                        <option value="">-- Choisir une spécialité --</option>
                        ${Array.from(document.querySelector('[name="specialites[0].id"]').options)
                .map(opt => `<option value="${opt.value}">${opt.text}</option>`)
                .join('')}
                    </select>
                </div>
                <div class="col-md-4">
                    <input type="number" name="specialites[${specialiteCounter}].nbPostes"
                           class="form-control" placeholder="Postes" min="1" required>
                </div>
            `;
            container.appendChild(newRow);
            specialiteCounter++;
        });

        // 2. Gestion des centres d'examen
        const centresSelect = document.getElementById('centresSelect');

        centresSelect.addEventListener('change', function() {
            centresSelectionnes = Array.from(this.selectedOptions).map(option => ({
                id: option.value,
                text: option.text
            }));
            updateCentresSelectionnes();
        });

        // 3. Gestion des conditions
        const conditionsInput = document.getElementById('conditionsInput');

        // Conditions pré-définies
        document.querySelectorAll('.condition-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const condition = this.value;
                if (this.checked && !conditionsSelectionnees.includes(condition)) {
                    conditionsSelectionnees.push(condition);
                } else {
                    conditionsSelectionnees = conditionsSelectionnees.filter(c => c !== condition);
                }
                updateConditionsAffichage();
            });
        });

        // Ajout de condition personnalisée
        document.getElementById('ajouterCondition').addEventListener('click', function() {
            const nouvelleCondition = document.getElementById('nouvelleCondition').value.trim();
            if (nouvelleCondition && !conditionsSelectionnees.includes(nouvelleCondition)) {
                conditionsSelectionnees.push(nouvelleCondition);
                document.getElementById('nouvelleCondition').value = '';
                updateConditionsAffichage();
            }
        });

        // 4. Soumission du formulaire
        document.getElementById('concoursForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Préparation des données
            const centresInput = document.getElementById('centresExamenIds');
            centresInput.value = centresSelectionnes.map(c => c.id).join(',');

            const conditionsInput = document.getElementById('conditionsInput');
            conditionsInput.value = conditionsSelectionnees.join('\n• ');

            // Validation
            if (validateForm()) {
                this.submit();
            }
        });

        function validateForm() {
            let isValid = true;

            // Validation des centres
            if (centresSelectionnes.length === 0) {
                document.getElementById('centres-error').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('centres-error').style.display = 'none';
            }

            // Validation des conditions
            if (conditionsSelectionnees.length === 0) {
                document.getElementById('conditions-error').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('conditions-error').style.display = 'none';
            }

            return isValid;
        }
    });

    // Fonctions globales
    function removeCentre(centreId) {
        const centresSelect = document.getElementById('centresSelect');
        centresSelectionnes = centresSelectionnes.filter(c => c.id !== centreId);

        Array.from(centresSelect.options).forEach(option => {
            if (option.value === centreId) {
                option.selected = false;
            }
        });

        updateCentresSelectionnes();
    }

    function removeCondition(condition) {
        conditionsSelectionnees = conditionsSelectionnees.filter(c => c !== condition);
        updateConditionsAffichage();

        document.querySelectorAll('.condition-checkbox').forEach(checkbox => {
            if (checkbox.value === condition) {
                checkbox.checked = false;
            }
        });
    }

    function updateConditionsAffichage() {
        const container = document.getElementById('conditionsSelectionnees');
        const conditionsInput = document.getElementById('conditionsInput');

        if (conditionsSelectionnees.length === 0) {
            container.innerHTML = '<p class="text-muted small-text mb-0">Aucune condition sélectionnée</p>';
        } else {
            container.innerHTML = conditionsSelectionnees.map(condition => `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>• ${condition}</span>
                    <button type="button" class="btn btn-sm btn-outline-danger"
                            onclick="removeCondition('${condition.replace(/'/g, "\\'")}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `).join('');
        }

        conditionsInput.value = conditionsSelectionnees.join('\n• ');
    }

    function updateCentresSelectionnes() {
        const container = document.getElementById('centresSelectionnees');
        const centresInput = document.getElementById('centresExamenIds');

        if (centresSelectionnes.length === 0) {
            container.innerHTML = '<p class="text-muted small-text mb-0">Aucun centre sélectionné</p>';
        } else {
            container.innerHTML = centresSelectionnes.map(centre => `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>${centre.text}</span>
                    <button type="button" class="btn btn-sm btn-outline-danger"
                            onclick="removeCentre('${centre.id}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `).join('');
        }

        centresInput.value = centresSelectionnes.map(c => c.id).join(',');
    }
</script>

</body>
</html>