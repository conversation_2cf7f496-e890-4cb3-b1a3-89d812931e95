<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Suivi de Candidature - MEF</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        :root {
            --bleu-mef: #0056b3;
            --gris-fond: #f8f9fa;
        }

        body {
            background-color: var(--gris-fond);
        }

        .suivi-container {
            max-width: 600px;
            margin: 50px auto;
            background-color: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .search-form {
            background: linear-gradient(135deg, var(--bleu-mef), #0079d3);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 20px;
        }

        .form-floating .form-control:focus {
            border-color: var(--bleu-mef);
            box-shadow: 0 0 0 0.2rem rgba(0, 86, 179, 0.25);
        }

        .btn-search {
            background-color: white;
            color: var(--bleu-mef);
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-search:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .help-section {
            background-color: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-top: 30px;
        }

        .help-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .help-icon {
            width: 40px;
            height: 40px;
            background-color: var(--bleu-mef);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .error-alert {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
<!-- En-tête -->
<header style="background-color: white; padding: 15px 0; border-bottom: 1px solid #e0e0e0;">
    <div class="container d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            <img src="https://imgs.search.brave.com/_sQmz83PoFynMn9wl0RNhXY0yh8yKwDM0fXEtyPgd3Y/rs:fit:860:0:0:0/g:ce/aHR0cHM6Ly91cGxv/YWQud2lraW1lZGlh/Lm9yZy93aWtpcGVk/aWEvY29tbW9ucy9j/L2NiL01FRl9NT1JP/Q0NPLnBuZw"
                 alt="Logo Ministère" style="height: 70px; margin-right: 15px;">
            <div>
                <h4 style="color: #0056b3; margin-bottom: 0;">Ministère de l'économie et des finances</h4>
                <p style="margin-bottom: 0; color: #6c757d;">Portail des Concours Administratifs</p>
            </div>
        </div>
        <div class="d-none d-md-block">
            <a th:href="@{/public/}" class="btn btn-outline-primary me-2">
                <i class="fas fa-home me-1"></i> Accueil
            </a>
            <a th:href="@{/public/candidature}" class="btn btn-primary">
                <i class="fas fa-file-alt me-1"></i> Postuler
            </a>
        </div>
    </div>
</header>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark" style="background-color: #0056b3;">
    <div class="container">
        <a class="navbar-brand" th:href="@{/public/}">
            <i class="fas fa-trophy me-2"></i>Concours MEF
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/public/}"><i class="fas fa-home me-1"></i> Accueil</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/public/concours}"><i class="fas fa-trophy me-1"></i> Concours</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" th:href="@{/public/suivi}"><i class="fas fa-search me-1"></i> Suivi</a>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Contenu principal -->
<div class="container">
    <div class="suivi-container">
        <div class="search-form">
            <h2 class="text-center mb-4">
                <i class="fas fa-search me-2"></i>
                Suivi de candidature
            </h2>
            <p class="text-center opacity-75 mb-4">
                Saisissez votre numéro de candidature et votre CIN pour consulter l'état de votre dossier
            </p>

            <!-- Message d'erreur -->
            <div th:if="${error}" class="error-alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span th:text="${error}">Message d'erreur</span>
            </div>

            <form th:action="@{/public/suivi}" method="post" id="suiviForm">
                <div class="row g-3">
                    <div class="col-12">
                        <div class="form-floating">
                            <input type="text" class="form-control" name="numero" id="numero"
                                   placeholder="Numéro de candidature" required
                                   th:value="${param.numero != null ? param.numero[0] : ''}"
                                   pattern="[A-Za-z0-9\-]+" title="Format: XXXX-XXXX-XXXX ou similaire">
                            <label for="numero">
                                <i class="fas fa-id-card me-2"></i>Numéro de candidature
                            </label>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-floating">
                            <input type="text" class="form-control" name="cin" id="cin"
                                   placeholder="CIN" required maxlength="20"
                                   th:value="${param.cin != null ? param.cin[0] : ''}">
                            <label for="cin">
                                <i class="fas fa-address-card me-2"></i>CIN
                            </label>
                        </div>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-search">
                            <i class="fas fa-search me-2"></i>Rechercher ma candidature
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Section d'aide -->
        <div class="help-section">
            <h5 class="mb-3">
                <i class="fas fa-question-circle me-2 text-primary"></i>
                Comment ça marche ?
            </h5>

            <div class="help-item">
                <div class="help-icon">
                    <i class="fas fa-1"></i>
                </div>
                <div>
                    <strong>Saisissez votre numéro de candidature</strong>
                    <small class="d-block text-muted">Le numéro reçu par email après soumission de votre dossier</small>
                </div>
            </div>

            <div class="help-item">
                <div class="help-icon">
                    <i class="fas fa-2"></i>
                </div>
                <div>
                    <strong>Entrez votre CIN</strong>
                    <small class="d-block text-muted">Pour des raisons de sécurité</small>
                </div>
            </div>

            <div class="help-item">
                <div class="help-icon">
                    <i class="fas fa-3"></i>
                </div>
                <div>
                    <strong>Consultez le statut</strong>
                    <small class="d-block text-muted">En attente, validée ou rejetée</small>
                </div>
            </div>
        </div>

        <!-- Informations supplémentaires -->
        <div class="mt-4">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-info-circle me-2 text-info"></i>
                        Informations utiles
                    </h6>
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-clock me-2 text-muted"></i>
                            Le traitement des candidatures peut prendre 5 à 10 jours ouvrables
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-envelope me-2 text-muted"></i>
                            Vous recevrez une notification par email/SMS en cas de changement de statut
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-phone me-2 text-muted"></i>
                            Pour toute question : +212 5 37 67 XX XX
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="text-center mt-4">
            <a th:href="@{/public/candidature}" class="btn btn-outline-primary me-2">
                <i class="fas fa-plus me-1"></i>Nouvelle candidature
            </a>
            <a th:href="@{/public/}" class="btn btn-outline-secondary">
                <i class="fas fa-home me-1"></i>Retour à l'accueil
            </a>
        </div>
    </div>
</div>

<!-- Pied de page -->
<footer style="background-color: #2c3e50; color: white; padding: 2rem 0; margin-top: 3rem;">
    <div class="container">
        <div class="row">
            <div class="col-md-6 text-center text-md-start">
                <p class="mb-0">&copy; 2025 Ministère de l'économie et des finances. Tous droits réservés.</p>
            </div>
            <div class="col-md-6 text-center text-md-end">
                <ul class="list-inline mb-0">
                    <li class="list-inline-item"><a href="#" class="text-white text-decoration-none">Contact</a></li>
                    <li class="list-inline-item"><a href="#" class="text-white text-decoration-none">Aide</a></li>
                </ul>
            </div>
        </div>
    </div>
</footer>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('suiviForm');
        const numeroInput = document.getElementById('numero');
        const cinInput = document.getElementById('cin');

        // Formatage automatique du numéro de candidature
        numeroInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });

        // Validation en temps réel
        form.addEventListener('submit', function(event) {
            let isValid = true;

            // Validation du numéro
            if (numeroInput.value.trim().length < 8) {
                showError(numeroInput, 'Le numéro de candidature doit contenir au moins 8 caractères');
                isValid = false;
            }

            // Validation du CIN
            if (cinInput.value.trim().length < 8) {
                showError(cinInput, 'Le CIN doit contenir au moins 8 caractères');
                isValid = false;
            }

            if (!isValid) {
                event.preventDefault();
            }
        });

        function showError(input, message) {
            input.classList.add('is-invalid');

            // Supprimer l'ancien message d'erreur
            const existingError = input.parentElement.querySelector('.invalid-feedback');
            if (existingError) {
                existingError.remove();
            }

            // Ajouter le nouveau message d'erreur
            const errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            errorDiv.textContent = message;
            input.parentElement.appendChild(errorDiv);

            // Supprimer l'erreur après saisie
            input.addEventListener('input', function() {
                this.classList.remove('is-invalid');
                const errorMsg = this.parentElement.querySelector('.invalid-feedback');
                if (errorMsg) {
                    errorMsg.remove();
                }
            }, { once: true });
        }

        // Animation d'entrée
        const container = document.querySelector('.suivi-container');
        container.style.opacity = '0';
        container.style.transform = 'translateY(20px)';

        setTimeout(() => {
            container.style.transition = 'all 0.6s ease';
            container.style.opacity = '1';
            container.style.transform = 'translateY(0)';
        }, 100);
    });
</script>
</body>
</html>