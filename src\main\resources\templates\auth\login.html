<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Connexion - Plateforme de Concours - MEF</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <style>
        :root {
            --bleu-mef: #0056b3;
            --rouge-mef: #d52b1e;
            --vert-mef: #4caf50;
            --or-mef: #ffc107;
            --gris-fond: #f8f9fa;
        }

        body {
            font-family: 'Se<PERSON>e <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--gris-fond) 0%, #e3f2fd 100%);
            min-height: 100vh;
        }

        .login-container {
            max-width: 480px;
            margin: 60px auto;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .login-title {
            background: linear-gradient(135deg, var(--bleu-mef), #003366);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .login-form {
            padding: 2rem;
        }

        .form-control {
            border-radius: 10px;
            padding: 12px 15px;
            border: 2px solid #e3e3e3;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--bleu-mef);
            box-shadow: 0 0 0 0.2rem rgba(0, 86, 179, 0.25);
        }

        .input-group-text {
            border-radius: 10px 0 0 10px;
            background-color: var(--gris-fond);
            border: 2px solid #e3e3e3;
            border-right: none;
        }

        .btn-mef-primary {
            background: linear-gradient(135deg, var(--bleu-mef), #003f80);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-mef-primary:hover {
            background: linear-gradient(135deg, #003f80, var(--bleu-mef));
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 86, 179, 0.3);
        }

        .btn-home {
            background-color: var(--gris-fond);
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-home:hover {
            background-color: #e9ecef;
            transform: translateY(-1px);
        }

        .forgot-password-link {
            color: var(--bleu-mef);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .forgot-password-link:hover {
            text-decoration: underline;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>

<div class="login-container">
    <div class="login-title">
        <h3><i class="fas fa-lock me-2"></i>Connexion</h3>
        <p class="mb-0">Accédez à votre espace de gestion</p>
    </div>

    <div class="login-form">
        <form th:action="@{/login}" method="post">

            <!-- Message d'erreur -->
            <div th:if="${param.error}" class="alert alert-danger mb-3">
                <i class="fas fa-exclamation-circle me-2"></i>
                Identifiants incorrects. Veuillez réessayer.
            </div>

            <!-- Message de déconnexion -->
            <div th:if="${param.logout}" class="alert alert-success mb-3">
                <i class="fas fa-check-circle me-2"></i>
                Vous avez été déconnecté avec succès.
            </div>

            <!-- Message de succès (mot de passe réinitialisé) -->
            <div th:if="${success}" class="alert alert-success mb-3">
                <i class="fas fa-check-circle me-2"></i>
                <span th:text="${success}">Message de succès</span>
            </div>

            <!-- Email -->
            <div class="mb-3">
                <label for="username" class="form-label fw-semibold">Adresse email</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-envelope text-muted"></i></span>
                    <input type="email" class="form-control" id="username" name="username"
                           placeholder="<EMAIL>" required>
                </div>
            </div>

            <!-- Mot de passe -->
            <div class="mb-3">
                <label for="password" class="form-label fw-semibold">Mot de passe</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-lock text-muted"></i></span>
                    <input type="password" class="form-control" id="password" name="password"
                           placeholder="Votre mot de passe" required>
                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>

            <!-- Se souvenir de moi et mot de passe oublié -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="remember-me" name="remember-me">
                    <label class="form-check-label" for="remember-me">Se souvenir de moi</label>
                </div>
                <a th:href="@{/auth/forgot-password}" class="forgot-password-link">
                    <i class="fas fa-key me-1"></i>Mot de passe oublié ?
                </a>
            </div>

            <!-- Bouton de connexion -->
            <div class="d-grid mb-3">
                <button type="submit" class="btn btn-mef-primary text-white">
                    <i class="fas fa-sign-in-alt me-2"></i> Se connecter
                </button>
            </div>

            <!-- Bouton Retour à l'accueil -->
            <div class="d-grid">
                <a th:href="@{/public/}" class="btn btn-home">
                    <i class="fas fa-home me-2"></i> Retour à l'accueil
                </a>
            </div>
        </form>
    </div>
</div>

<!-- JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    document.getElementById('togglePassword').addEventListener('click', function () {
        const input = document.getElementById('password');
        const icon = this.querySelector('i');
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.replace('fa-eye', 'fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.replace('fa-eye-slash', 'fa-eye');
        }
    });

    // Auto-focus sur le champ email
    document.getElementById('username').focus();
</script>
</body>
</html>
