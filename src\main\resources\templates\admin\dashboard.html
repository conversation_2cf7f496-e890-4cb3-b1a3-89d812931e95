<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Dashboard Admin - MEF</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #1a56db;
            --secondary-color: #046c4e;
            --accent-color: #9061f9;
            --warning-color: #d97706;
            --sidebar-bg: #ffffff;
            --sidebar-width: 280px;
            --text-color: #1f2937;
            --light-bg: #f9fafb;
            --border-color: #e5e7eb;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-color);
            margin: 0;
            line-height: 1.6;
        }

        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            padding: 1.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .sidebar-header {
            padding: 0.5rem 0 1.5rem;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 1.5rem;
        }

        .sidebar-header h4 {
            color: var(--primary-color);
            font-weight: 700;
            margin: 0;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
        }

        .sidebar-header h4 i {
            margin-right: 0.75rem;
            color: var(--primary-color);
        }

        .sidebar .nav-link {
            color: var(--text-color);
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .sidebar .nav-link i {
            width: 1.5rem;
            margin-right: 0.75rem;
            color: var(--primary-color);
            text-align: center;
        }

        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background-color: rgba(26, 86, 219, 0.1);
            color: var(--primary-color);
        }

        .sidebar .nav-link.active {
            font-weight: 600;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            padding: 2rem;
            min-height: 100vh;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .welcome-text h5 {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .welcome-text small {
            color: #6b7280;
            font-size: 0.875rem;
        }

        .stat-card {
            border: none;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border-radius: 0.75rem;
            padding: 1.5rem;
            background-color: white;
            height: 100%;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-top: 4px solid var(--primary-color);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .stat-card.stat-card-success {
            border-top-color: var(--secondary-color);
        }

        .stat-card.stat-card-warning {
            border-top-color: var(--warning-color);
        }

        .stat-card.stat-card-purple {
            border-top-color: var(--accent-color);
        }

        .stat-card i {
            font-size: 1.75rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .stat-card.stat-card-success i {
            color: var(--secondary-color);
        }

        .stat-card.stat-card-warning i {
            color: var(--warning-color);
        }

        .stat-card.stat-card-purple i {
            color: var(--accent-color);
        }

        .stat-card h3 {
            margin-bottom: 0.5rem;
            font-weight: 700;
            font-size: 2rem;
        }

        .stat-card p {
            color: #6b7280;
            margin-bottom: 1.25rem;
        }

        .btn-outline-custom {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .btn-outline-custom:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-outline-success {
            border-color: var(--secondary-color);
            color: var(--secondary-color);
        }

        .btn-outline-success:hover {
            background-color: var(--secondary-color);
            color: white;
        }

        .btn-outline-warning {
            border-color: var(--warning-color);
            color: var(--warning-color);
        }

        .btn-outline-warning:hover {
            background-color: var(--warning-color);
            color: white;
        }

        .btn-outline-purple {
            border-color: var(--accent-color);
            color: var(--accent-color);
        }

        .btn-outline-purple:hover {
            background-color: var(--accent-color);
            color: white;
        }

        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .main-content {
                margin-left: 0;
            }
        }

        /* Animation for cards */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .stat-card {
            animation: fadeIn 0.5s ease forwards;
        }

        .stat-card:nth-child(2) {
            animation-delay: 0.1s;
        }

        .stat-card:nth-child(3) {
            animation-delay: 0.2s;
        }
    </style>
</head>
<body>
<div class="sidebar">
    <div class="sidebar-header">
        <h4><i class="fas fa-tachometer-alt"></i> Tableau de bord</h4>
    </div>
    <nav class="nav flex-column">
        <a th:href="@{/admin/dashboard}" class="nav-link"><i class="fas fa-home"></i> Accueil</a>
        <a th:href="@{/admin/gestion-candidatures/list}" class="nav-link"><i class="fas fa-folder-open"></i> Candidatures</a>
        <a th:href="@{/admin/gestion-concours/list}" class="nav-link"><i class="fas fa-trophy"></i> Concours</a>
        <a th:href="@{/admin/centres/list}" class="nav-link"><i class="fas fa-building"></i> Centres</a>
        <a th:href="@{/admin/gestion-utilisateurs/list}" class="nav-link"><i class="fas fa-users-cog"></i> Utilisateurs</a>
        <a th:href="@{/admin/reporting/statistiques}" class="nav-link active"><i class="fas fa-chart-line"></i> Statistiques</a>
        <a th:href="@{/admin/profile}" class="nav-link"><i class="fas fa-user-edit"></i> Profil</a>
    </nav>
</div>

<div class="main-content">
    <div class="header">
        <div class="welcome-text">
            <h5>Bienvenue, <strong>Admin</strong></h5>
            <small id="currentTime"></small>
        </div>
        <form th:action="@{/auth/logout}" method="post" class="d-inline">
            <button type="submit" class="btn btn-outline-danger">
                <i class="fas fa-sign-out-alt me-1"></i> Déconnexion
            </button>
        </form>
    </div>

    <div class="row g-4">
        <div class="col-md-4">
            <div class="stat-card text-center">
                <i class="fas fa-trophy"></i>
                <h3 th:text="${stats.nbConcours}">0</h3>
                <p>Concours actifs</p>
                <a th:href="@{/admin/gestion-concours/list}" class="btn btn-outline-custom btn-sm">Voir détails</a>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stat-card stat-card-success text-center">
                <i class="fas fa-user-check"></i>
                <h3 th:text="${stats.nbCandidatures}">0</h3>
                <p>Candidatures enregistrées</p>
                <a th:href="@{/admin/gestion-candidatures/list}" class="btn btn-outline-success btn-sm">Voir détails</a>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stat-card stat-card-warning text-center">
                <i class="fas fa-users"></i>
                <h3 th:text="${stats.nbUtilisateurs}">0</h3>
                <p>Utilisateurs actifs</p>
                <a th:href="@{/admin/gestion-utilisateurs/list}" class="btn btn-outline-warning btn-sm">Voir détails</a>
            </div>
        </div>
    </div>
</div>

<script>
    function updateTime() {
        const now = new Date();
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        document.getElementById("currentTime").textContent = now.toLocaleDateString('fr-FR', options);
    }
    setInterval(updateTime, 1000);
    updateTime();
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>