package com.concours.entity;

import jakarta.persistence.*;
import lombok.*;
import java.time.LocalDate;
import java.util.List;

@Entity
@Data @NoArgsConstructor @AllArgsConstructor
public class Candidature {
    @Id
    private String numero;

    @Enumerated(EnumType.STRING)
    private StatutCandidature statut = StatutCandidature.EN_ATTENTE;

    private LocalDate dateDepot = LocalDate.now();

    @Enumerated(EnumType.STRING)
    private TypeNotification notifications;

    @Column(name = "conditions_acceptees")
    private boolean accepter;

    @OneToMany(mappedBy = "candidature", cascade = CascadeType.ALL)
    private List<Document> documents;

    @ManyToOne
    @JoinColumn(name = "candidat_id", nullable = false)
    private Candidat candidat;

    @ManyToOne
    @JoinColumn(name = "concours_id", nullable = false)
    private Concours concours;

    @ManyToOne
    @JoinColumn(name = "specialite_id", nullable = false)
    private Specialite specialite;

    @ManyToOne
    @JoinColumn(name = "centre_examen_id", nullable = false)
    private CentreExamen centreExamen;

    @ManyToOne
    @JoinColumn(name = "utilisateur_traitant_id")
    private Utilisateur utilisateurTraitant;

    // Méthode utilitaire pour ajouter des documents
    public void addDocument(Document document) {
        documents.add(document);
        document.setCandidature(this);
    }
}