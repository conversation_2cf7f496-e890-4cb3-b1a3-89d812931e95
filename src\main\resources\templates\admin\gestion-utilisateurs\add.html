<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Nouvel utilisateur</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        function toggleCentresSelection() {
            var roleSelect = document.getElementById('role');
            var centresDiv = document.getElementById('centresSelection');

            if (roleSelect.value === 'ROLE_GESTIONNAIRE_LOCAL') {
                centresDiv.style.display = 'block';
            } else {
                centresDiv.style.display = 'none';
                // Désélectionner tous les centres
                var checkboxes = document.querySelectorAll('input[name="selectedCentres"]');
                checkboxes.forEach(function(checkbox) {
                    checkbox.checked = false;
                });
            }
        }
    </script>
</head>
<body>
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-user-plus me-2"></i>Créer un nouvel utilisateur</h4>
                </div>
                <div class="card-body">
                    <form th:action="@{/admin/utilisateurs/add}" method="post" th:object="${utilisateur}">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">Nom d'utilisateur *</label>
                                <input type="text" class="form-control" id="username" th:field="*{username}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" th:field="*{email}" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">Mot de passe *</label>
                                <input type="password" class="form-control" id="password" th:field="*{password}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">Rôle *</label>
                                <select class="form-select" id="role" th:field="*{role}" required onchange="toggleCentresSelection()">
                                    <option value="">-- Sélectionner un rôle --</option>
                                    <option value="ROLE_ADMIN">Administrateur</option>
                                    <option value="ROLE_GESTIONNAIRE_GLOBAL">Gestionnaire Global</option>
                                    <option value="ROLE_GESTIONNAIRE_LOCAL">Gestionnaire Local</option>
                                </select>
                            </div>
                        </div>

                        <!-- Section pour la sélection des centres (visible seulement pour ROLE_GESTIONNAIRE_LOCAL) -->
                        <div class="mb-3" id="centresSelection" style="display: none;">
                            <label class="form-label">Centres à affecter *</label>
                            <div class="border p-3 rounded" style="max-height: 200px; overflow-y: auto;">
                                <div th:each="centre : ${centres}" class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                           th:id="'centre_' + ${centre.id}"
                                           th:value="${centre.id}"
                                           th:field="*{selectedCentres}">
                                    <label class="form-check-label" th:for="'centre_' + ${centre.id}">
                                        <span th:text="${centre.code}"></span> -
                                        <span th:text="${centre.villeNom}"></span>
                                        (Capacité: <span th:text="${centre.capacite}"></span>)
                                    </label>
                                </div>
                            </div>
                            <small class="text-muted">Sélectionnez les centres que ce gestionnaire local pourra gérer</small>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enabled" th:field="*{enabled}" checked>
                                <label class="form-check-label" for="enabled">
                                    Compte activé
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a th:href="@{/admin/gestion-utilisateurs/list}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i> Créer l'utilisateur
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Initialiser l'état au chargement de la page
    document.addEventListener('DOMContentLoaded', function() {
        toggleCentresSelection();
    });
</script>
</body>
</html>