# ======================
# Nom de l'application
# ======================
spring.application.name=gestion-candidatures

# ======================
# Configuration MySQL
# ======================
spring.datasource.url=*****************************************************************************************************
spring.datasource.username=root
spring.datasource.password=Chmo1997
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# ======================
# Configuration JPA / Hibernate
# ======================
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true

# ======================
# Configuration Thymeleaf
# ======================
spring.thymeleaf.cache=false
logging.level.org.springframework=DEBUG

# ======================
# Upload de fichiers - CONFIGURATION CORRIG�E
# ======================
# Configuration multipart
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=500MB
spring.servlet.multipart.file-size-threshold=2MB
spring.servlet.multipart.resolve-lazily=false

# Configuration Tomcat - CRITIQUE POUR R�SOUDRE L'ERREUR
server.tomcat.max-http-form-post-size=500MB
server.tomcat.max-swallow-size=500MB
# Ces param�tres sont cruciaux pour r�soudre FileCountLimitExceededException
server.tomcat.max-connections=8192
server.tomcat.accept-count=100
server.tomcat.max-threads=200

# Param�tres sp�cifiques pour les uploads multipart
server.tomcat.connection-timeout=60000
server.tomcat.max-parameter-count=200000

# R�pertoire d'upload
file.upload-dir=uploads/
file.upload.dir=uploads/
file.max-size=100MB

# ======================
# Configuration Email
# ======================
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=mahdi@2003
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
management.health.mail.enabled=false

# ======================
# JWT
# ======================
app.jwt.secret=votreSecretTresSecure1234567890
app.jwt.expiration=86400000

# ======================
# Port du serveur
# ======================
server.port=8080