package com.concours.mapper;

import com.concours.dto.DocumentDTO;
import com.concours.entity.Document;
import org.mapstruct.*;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DocumentMapper {

    @Mapping(source = "cheminFichier", target = "nom", qualifiedByName = "extractFileName")
    DocumentDTO toDTO(Document document);

    Document toEntity(DocumentDTO documentDTO);

    @Named("extractFileName")
    default String extractFileName(String cheminFichier) {
        if (cheminFichier == null || cheminFichier.isEmpty()) {
            return "";
        }
        return cheminFichier.substring(cheminFichier.lastIndexOf("/") + 1);
    }
}