//package com.concours.config;
//
//import org.apache.catalina.connector.Connector;
//import org.apache.coyote.http11.Http11NioProtocol;
//import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
//import org.springframework.boot.web.embedded.tomcat.TomcatWebServer;
//import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//public class EmbeddedTomcatCustomizer {
//
//    @Bean
//    public ConfigurableServletWebServerFactory webServerFactory() {
//        TomcatServletWebServerFactory factory = new TomcatServletWebServerFactory() {
//            @Override
//            protected void postProcessContext(org.apache.catalina.Context context) {
//                super.postProcessContext(context);
//                // Forcer la création d'un ServletFileUpload personnalisé
//                context.addLifecycleListener(event -> {
//                    if ("configure_start".equals(event.getType())) {
//                        System.setProperty("org.apache.tomcat.util.http.fileupload.impl.FileCountLimitExceededException", "false");
//                    }
//                });
//            }
//        };
//
//        factory.addConnectorCustomizers(connector -> {
//            Http11NioProtocol protocol = (Http11NioProtocol) connector.getProtocolHandler();
//
//            // Configuration drastique pour éviter FileCountLimitExceededException
//            protocol.setMaxParameterCount(-1); // Pas de limite
//            protocol.setMaxPostSize(-1); // Pas de limite de taille POST
//            protocol.setMaxSwallowSize(-1); // Pas de limite de swallow
//            protocol.setMaxHttpHeaderSize(8192 * 8); // 64KB pour les headers
//            protocol.setConnectionTimeout(300000); // 5 minutes
//            protocol.setConnectionUploadTimeout(300000); // 5 minutes pour upload
//            protocol.setDisableUploadTimeout(false);
//
//            // Configuration du pool de threads
//            protocol.setMaxConnections(8192);
//            protocol.setAcceptCount(1000);
//            protocol.setMaxThreads(400);
//            protocol.setMinSpareThreads(25);
//
//            System.out.println("=== TOMCAT CONFIGURATION APPLIQUÉE ===");
//            System.out.println("MaxParameterCount: " + protocol.getMaxParameterCount());
//            System.out.println("MaxPostSize: " + protocol.getMaxPostSize());
//            System.out.println("MaxSwallowSize: " + protocol.getMaxSwallowSize());
//            System.out.println("MaxConnections: " + protocol.getMaxConnections());
//        });
//
//        return factory;
//    }
//}