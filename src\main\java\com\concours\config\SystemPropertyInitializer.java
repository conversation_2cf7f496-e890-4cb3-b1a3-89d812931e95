package com.concours.config;

import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Component;

@Component
public class SystemPropertyInitializer {

    @PostConstruct
    public void initSystemProperties() {
        // Désactiver complètement les limites de count de fichiers
        System.setProperty("org.apache.tomcat.util.http.fileupload.FileUploadBase.FILE_COUNT_MAX", "-1");
        System.setProperty("org.apache.tomcat.util.http.fileupload.impl.FileCountLimitExceededException.enabled", "false");

        // Configuration des limites Tomcat via system properties
        System.setProperty("tomcat.util.http.fileupload.maxParameterCount", "-1");
        System.setProperty("tomcat.util.http.parser.HttpParser.maxParameterCount", "-1");

        // Configuration générale
        System.setProperty("spring.servlet.multipart.enabled", "true");
        System.setProperty("spring.servlet.multipart.max-file-size", "100MB");
        System.setProperty("spring.servlet.multipart.max-request-size", "500MB");

        System.out.println("=== SYSTEM PROPERTIES CONFIGURÉES ===");
        System.out.println("FILE_COUNT_MAX: " + System.getProperty("org.apache.tomcat.util.http.fileupload.FileUploadBase.FILE_COUNT_MAX"));
        System.out.println("maxParameterCount: " + System.getProperty("tomcat.util.http.fileupload.maxParameterCount"));
    }
}