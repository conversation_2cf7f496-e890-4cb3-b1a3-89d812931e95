package com.concours.service;

import com.concours.dto.StatistiquesDTO;
import com.concours.entity.StatutCandidature;
import com.concours.repository.CandidatureRepository;
import com.concours.repository.CentreExamenRepository;
import com.concours.repository.ConcoursRepository;
import com.concours.repository.UtilisateurRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
@Slf4j
public class StatistiquesService {

    private final CandidatureRepository candidatureRepository;
    private final ConcoursRepository concoursRepository;
    private final UtilisateurRepository utilisateurRepository;
    private final CentreExamenRepository centreExamenRepository;

    /**
     * Statistiques optimisées pour la page d'accueil publique
     */
    public StatistiquesDTO getStatistiquesAccueil() {
        StatistiquesDTO stats = new StatistiquesDTO();

        try {
            // Utiliser des count optimisés
            Long nbCandidaturesValidees = candidatureRepository.countByStatut(StatutCandidature.VALIDEE);
            stats.setNbCandidatures(nbCandidaturesValidees != null ? nbCandidaturesValidees : 0L);

            log.debug("Statistiques d'accueil calculées: {} candidatures validées", stats.getNbCandidatures());

        } catch (Exception e) {
            log.error("Erreur lors du calcul des statistiques d'accueil", e);
            stats.setNbCandidatures(0L);
        }

        return stats;
    }

    /**
     * Statistiques globales pour le dashboard admin (avec cache pour performance)
     */
    @Cacheable(value = "statistiques", key = "'globales'")
    public StatistiquesDTO getStatistiquesGlobales() {
        StatistiquesDTO stats = new StatistiquesDTO();

        try {
            log.info("Calcul des statistiques globales...");

            // Utiliser des appels parallèles pour améliorer les performances
            CompletableFuture<Long> totalCandidaturesFuture = CompletableFuture.supplyAsync(() ->
                    candidatureRepository.count());
            CompletableFuture<Long> candidaturesValideesFuture = CompletableFuture.supplyAsync(() ->
                    candidatureRepository.countByStatut(StatutCandidature.VALIDEE));
            CompletableFuture<Long> candidaturesEnAttenteFuture = CompletableFuture.supplyAsync(() ->
                    candidatureRepository.countByStatut(StatutCandidature.EN_ATTENTE));
            CompletableFuture<Long> candidaturesRejeteesFuture = CompletableFuture.supplyAsync(() ->
                    candidatureRepository.countByStatut(StatutCandidature.REJETEE));
            CompletableFuture<Long> nbConcoursFuture = CompletableFuture.supplyAsync(() ->
                    concoursRepository.count());
            CompletableFuture<Long> nbUtilisateursFuture = CompletableFuture.supplyAsync(() ->
                    utilisateurRepository.count());

            // Attendre tous les résultats
            CompletableFuture.allOf(totalCandidaturesFuture, candidaturesValideesFuture,
                    candidaturesEnAttenteFuture, candidaturesRejeteesFuture,
                    nbConcoursFuture, nbUtilisateursFuture).join();

            // Récupérer les résultats
            stats.setTotalCandidatures(totalCandidaturesFuture.get());
            stats.setCandidaturesValidees(candidaturesValideesFuture.get());
            stats.setCandidaturesEnAttente(candidaturesEnAttenteFuture.get());
            stats.setCandidaturesRejetees(candidaturesRejeteesFuture.get());
            stats.setNbConcours(nbConcoursFuture.get());
            stats.setNbUtilisateurs(nbUtilisateursFuture.get());
            stats.setNbCentres(centreExamenRepository.count());

            // Calculer les statistiques détaillées
            stats.setCandidaturesParConcours(getCandidaturesParConcours());
            stats.setCandidaturesParSpecialite(getCandidaturesParSpecialite());
            stats.setCandidaturesParCentre(getCandidaturesParCentre());
            stats.setCandidaturesParMois(getCandidaturesParMois());

            log.info("Statistiques globales calculées avec succès");

        } catch (Exception e) {
            log.error("Erreur lors du calcul des statistiques globales", e);
            initDefaultStats(stats);
        }

        return stats;
    }

    /**
     * Statistiques rapides pour l'administration
     */
    public StatistiquesDTO getStatistiquesAdmin() {
        StatistiquesDTO stats = new StatistiquesDTO();

        try {
            stats.setNbConcours(concoursRepository.count());
            stats.setNbCandidatures(candidatureRepository.count());
            stats.setNbUtilisateurs(utilisateurRepository.count());
            stats.setNbCentres(centreExamenRepository.count());

            // Statistiques par statut
            stats.setCandidaturesValidees(candidatureRepository.countByStatut(StatutCandidature.VALIDEE));
            stats.setCandidaturesEnAttente(candidatureRepository.countByStatut(StatutCandidature.EN_ATTENTE));
            stats.setCandidaturesRejetees(candidatureRepository.countByStatut(StatutCandidature.REJETEE));

        } catch (Exception e) {
            log.error("Erreur lors du calcul des statistiques admin", e);
            initDefaultStats(stats);
        }

        return stats;
    }

    // Méthodes privées pour calculer les statistiques détaillées

    private Map<String, Long> getCandidaturesParConcours() {
        try {
            List<Map<String, Object>> results = candidatureRepository.getCandidaturesParConcours();
            return results.stream()
                    .collect(Collectors.toMap(
                            map -> (String) map.get("concours"),
                            map -> (Long) map.get("count")
                    ));
        } catch (Exception e) {
            log.warn("Erreur lors du calcul des candidatures par concours", e);
            return new HashMap<>();
        }
    }

    private Map<String, Long> getCandidaturesParSpecialite() {
        try {
            List<Map<String, Object>> results = candidatureRepository.getCandidaturesParSpecialite();
            return results.stream()
                    .collect(Collectors.toMap(
                            map -> (String) map.get("specialite"),
                            map -> (Long) map.get("count")
                    ));
        } catch (Exception e) {
            log.warn("Erreur lors du calcul des candidatures par spécialité", e);
            return new HashMap<>();
        }
    }

    private Map<String, Long> getCandidaturesParCentre() {
        try {
            List<Map<String, Object>> results = candidatureRepository.getCandidaturesParCentre();
            return results.stream()
                    .collect(Collectors.toMap(
                            map -> (String) map.get("centre"),
                            map -> (Long) map.get("count")
                    ));
        } catch (Exception e) {
            log.warn("Erreur lors du calcul des candidatures par centre", e);
            return new HashMap<>();
        }
    }

    private Map<String, Long> getCandidaturesParMois() {
        try {
            List<Map<String, Object>> results = candidatureRepository.getCandidaturesParMois();
            return results.stream()
                    .collect(Collectors.toMap(
                            map -> (String) map.get("mois"),
                            map -> (Long) map.get("count")
                    ));
        } catch (Exception e) {
            log.warn("Erreur lors du calcul des candidatures par mois", e);
            return new HashMap<>();
        }
    }

    private void initDefaultStats(StatistiquesDTO stats) {
        stats.setNbConcours(0);
        stats.setNbCandidatures(0);
        stats.setNbUtilisateurs(0);
        stats.setNbCentres(0L);
        stats.setTotalCandidatures(0);
        stats.setCandidaturesValidees(0);
        stats.setCandidaturesEnAttente(0);
        stats.setCandidaturesRejetees(0);
        stats.setNbPostes(0);
        stats.setCandidaturesParConcours(new HashMap<>());
        stats.setCandidaturesParSpecialite(new HashMap<>());
        stats.setCandidaturesParCentre(new HashMap<>());
        stats.setCandidaturesParMois(new HashMap<>());
    }

    /**
     * Méthode pour invalider le cache des statistiques (à appeler après des modifications importantes)
     */
    // @CacheEvict(value = "statistiques", allEntries = true)
    public void invaliderCacheStatistiques() {
        log.info("Cache des statistiques invalidé");
    }
}