<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Formulaire de Candidature - MEF</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        :root {
            --bleu-mef: #0056b3;
            --gris-fond: #f8f9fa;
            --rouge-erreur: #dc3545;
            --vert-succes: #28a745;
        }

        body {
            background-color: var(--gris-fond);
        }

        .form-container {
            max-width: 900px;
            margin: 30px auto;
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .ministere-header {
            background-color: white;
            padding: 10px 0;
            border-bottom: 1px solid #e0e0e0;
        }

        h2 {
            text-align: center;
            color: var(--bleu-mef);
            margin-bottom: 30px;
        }

        .form-label {
            font-weight: 500;
            color: #333;
        }

        .form-section-title {
            color: var(--bleu-mef);
            border-bottom: 2px solid var(--bleu-mef);
            padding-bottom: 8px;
            margin-bottom: 20px;
        }

        .btn-mef {
            background-color: var(--bleu-mef);
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 8px;
            border: none;
            margin-top: 20px;
            transition: all 0.3s ease;
        }

        .btn-mef:hover {
            background-color: #003f80;
            transform: translateY(-2px);
        }

        .btn-home {
            background-color: var(--gris-fond);
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px 24px;
            font-weight: 500;
            margin-top: 15px;
            text-align: center;
            display: inline-block;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
        }

        .btn-home:hover {
            background-color: #e9ecef;
            text-decoration: none;
            color: #333;
        }

        .error-message {
            color: var(--rouge-erreur);
            font-size: 0.875rem;
            margin-top: 5px;
        }

        .form-control:focus,
        .form-select:focus {
            border-color: var(--bleu-mef);
            box-shadow: 0 0 0 0.2rem rgba(0, 86, 179, 0.25);
        }

        .alert-custom {
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .alert-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .file-input-wrapper {
            position: relative;
        }

        .file-input-info {
            font-size: 0.875rem;
            color: #666;
            margin-top: 5px;
        }

        .required-field::after {
            content: " *";
            color: var(--rouge-erreur);
        }

        .progress {
            height: 4px;
            margin-bottom: 20px;
            display: none;
        }

        .is-invalid {
            border-color: var(--rouge-erreur);
        }

        .is-valid {
            border-color: var(--vert-succes);
        }
    </style>
</head>
<body>
<!-- En-tête et navigation (inchangé) -->
<header class="ministere-header">
    <div class="container d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            <img src="https://imgs.search.brave.com/_sQmz83PoFynMn9wl0RNhXY0yh8yKwDM0fXEtyPgd3Y/rs:fit:860:0:0:0/g:ce/aHR0cHM6Ly91cGxv/YWQud2lraW1lZGlh/Lm9yZy93aWtpcGVk/aWEvY29tbW9ucy9j/L2NiL01FRl9NT1JP/Q0NPLnBuZw"
                 alt="Logo Ministère" style="height: 90px; margin-right: 15px;">
            <div>
                <h4 style="color: #0056b3; margin-bottom: 0;">Ministère de l'économie et des finances</h4>
                <p style="margin-bottom: 0; color: #6c757d;">Portail des Concours Administratifs</p>
            </div>
        </div>
        <div class="d-none d-md-block">
            <a th:href="@{/public/}" class="btn btn-outline-primary me-2">
                <i class="fas fa-home me-1"></i> Accueil
            </a>
            <a th:href="@{/public/suivi}" class="btn btn-outline-secondary">
                <i class="fas fa-search me-1"></i> Suivi
            </a>
        </div>
    </div>
</header>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark" style="background-color: #0056b3;">
    <div class="container">
        <a class="navbar-brand" th:href="@{/public/}">
            <i class="fas fa-trophy me-2"></i>Concours MEF
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/public/}"><i class="fas fa-home me-1"></i> Accueil</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/public/concours}"><i class="fas fa-trophy me-1"></i> Concours</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" th:href="@{/public/candidature}"><i class="fas fa-file-alt me-1"></i> Candidature</a>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Contenu principal -->
<div class="container py-4">
    <div class="form-container">
        <h2><i class="fas fa-file-alt me-2"></i>Formulaire de Candidature</h2>

        <!-- Barre de progression -->
        <div class="progress" id="progressBar">
            <div class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
        </div>

        <!-- Messages d'erreur/succès -->
        <div th:if="${error}" class="alert alert-danger alert-custom">
            <i class="fas fa-exclamation-circle me-2"></i>
            <span th:text="${error}">Message d'erreur</span>
        </div>

        <div th:if="${success}" class="alert alert-success alert-custom">
            <i class="fas fa-check-circle me-2"></i>
            <span th:text="${success}">Message de succès</span>
        </div>

        <form th:action="@{/public/candidature}" method="post" enctype="multipart/form-data"
              th:object="${candidatureDto}" id="candidatureForm" novalidate>

            <!-- Informations personnelles -->
            <div class="row mb-4">
                <h5 class="form-section-title">
                    <i class="fas fa-user me-2"></i>Informations personnelles
                </h5>

                <div class="col-md-6 mb-3">
                    <label class="form-label required-field">Nom</label>
                    <input type="text" th:field="*{nom}" class="form-control" required maxlength="100">
                    <div class="error-message" th:if="${#fields.hasErrors('nom')}" th:errors="*{nom}"></div>
                </div>

                <div class="col-md-6 mb-3">
                    <label class="form-label required-field">Prénom</label>
                    <input type="text" th:field="*{prenom}" class="form-control" required maxlength="100">
                    <div class="error-message" th:if="${#fields.hasErrors('prenom')}" th:errors="*{prenom}"></div>
                </div>

                <div class="col-md-6 mb-3">
                    <label class="form-label required-field">CIN</label>
                    <input type="text" th:field="*{cin}" class="form-control" required maxlength="20"
                           placeholder="Ex: AB123456">
                    <div class="error-message" th:if="${#fields.hasErrors('cin')}" th:errors="*{cin}"></div>
                </div>

                <div class="col-md-6 mb-3">
                    <label class="form-label required-field">Sexe</label>
                    <select th:field="*{sexe}" class="form-select" required>
                        <option value="">-- Sélectionner --</option>
                        <option value="HOMME">Homme</option>
                        <option value="FEMME">Femme</option>
                    </select>
                    <div class="error-message" th:if="${#fields.hasErrors('sexe')}" th:errors="*{sexe}"></div>
                </div>

                <div class="col-md-6 mb-3">
                    <label class="form-label required-field">Date de naissance</label>
                    <input type="date" th:field="*{dateNaissance}" class="form-control" required>
                    <div class="error-message" th:if="${#fields.hasErrors('dateNaissance')}" th:errors="*{dateNaissance}"></div>
                </div>

                <div class="col-md-6 mb-3">
                    <label class="form-label required-field">Lieu de naissance</label>
                    <select th:field="*{lieuNaissanceId}" class="form-select" required>
                        <option value="">-- Choisir une ville --</option>
                        <option th:each="ville : ${villes}" th:value="${ville.id}" th:text="${ville.nom}"></option>
                    </select>
                    <div class="error-message" th:if="${#fields.hasErrors('lieuNaissanceId')}" th:errors="*{lieuNaissanceId}"></div>
                </div>
            </div>

            <!-- Coordonnées -->
            <div class="row mb-4">
                <h5 class="form-section-title">
                    <i class="fas fa-address-card me-2"></i>Coordonnées
                </h5>

                <div class="col-12 mb-3">
                    <label class="form-label required-field">Adresse</label>
                    <input type="text" th:field="*{adresse}" class="form-control" required maxlength="255"
                           placeholder="Adresse complète">
                    <div class="error-message" th:if="${#fields.hasErrors('adresse')}" th:errors="*{adresse}"></div>
                </div>

                <div class="col-md-6 mb-3">
                    <label class="form-label required-field">Ville de résidence</label>
                    <select th:field="*{villeResidenceId}" class="form-select" required>
                        <option value="">-- Choisir une ville --</option>
                        <option th:each="ville : ${villes}" th:value="${ville.id}" th:text="${ville.nom}"></option>
                    </select>
                    <div class="error-message" th:if="${#fields.hasErrors('villeResidenceId')}" th:errors="*{villeResidenceId}"></div>
                </div>

                <div class="col-md-6 mb-3">
                    <label class="form-label required-field">Email</label>
                    <input type="email" th:field="*{email}" class="form-control" required maxlength="100"
                           placeholder="<EMAIL>">
                    <div class="error-message" th:if="${#fields.hasErrors('email')}" th:errors="*{email}"></div>
                </div>

                <div class="col-md-6 mb-3">
                    <label class="form-label required-field">Téléphone</label>
                    <input type="tel" th:field="*{telephone}" class="form-control" required maxlength="20"
                           placeholder="0X XX XX XX XX">
                    <div class="error-message" th:if="${#fields.hasErrors('telephone')}" th:errors="*{telephone}"></div>
                </div>
            </div>

            <!-- Formation -->
            <div class="row mb-4">
                <h5 class="form-section-title">
                    <i class="fas fa-graduation-cap me-2"></i>Formation et Expérience
                </h5>

                <div class="col-md-6 mb-3">
                    <label class="form-label required-field">Niveau d'études</label>
                    <input type="text" th:field="*{niveauEtude}" class="form-control" required maxlength="100"
                           placeholder="Ex: Licence, Master, Doctorat">
                    <div class="error-message" th:if="${#fields.hasErrors('niveauEtude')}" th:errors="*{niveauEtude}"></div>
                </div>

                <div class="col-md-6 mb-3">
                    <label class="form-label required-field">Diplôme</label>
                    <input type="text" th:field="*{diplome}" class="form-control" required maxlength="100"
                           placeholder="Intitulé du diplôme">
                    <div class="error-message" th:if="${#fields.hasErrors('diplome')}" th:errors="*{diplome}"></div>
                </div>

                <div class="col-12 mb-3">
                    <label class="form-label">Expérience professionnelle</label>
                    <textarea th:field="*{experience}" class="form-control" rows="4" maxlength="1000"
                              placeholder="Décrivez brièvement votre expérience professionnelle (optionnel)"></textarea>
                    <small class="text-muted">Maximum 1000 caractères</small>
                    <div class="error-message" th:if="${#fields.hasErrors('experience')}" th:errors="*{experience}"></div>
                </div>
            </div>

            <!-- Concours -->
            <div class="row mb-4">
                <h5 class="form-section-title">
                    <i class="fas fa-trophy me-2"></i>Sélection du Concours
                </h5>

                <div class="col-md-6 mb-3">
                    <label class="form-label required-field">Concours</label>
                    <select th:field="*{concoursId}" class="form-select" required id="concoursSelect">
                        <option value="">-- Sélectionner un concours --</option>
                        <option th:each="c : ${concoursList}" th:value="${c.id}"
                                th:text="${c.titre }"></option>
                    </select>
                    <div class="error-message" th:if="${#fields.hasErrors('concoursId')}" th:errors="*{concoursId}"></div>
                </div>

                <div class="col-md-6 mb-3">
                    <label class="form-label required-field">Spécialité</label>
                    <select th:field="*{specialiteId}" class="form-select" required id="specialiteSelect">
                        <option value="">-- Sélectionner une spécialité --</option>
                        <option th:each="s : ${specialites}" th:value="${s.id}" th:text="${s.libelle}"></option>
                    </select>
                    <div class="error-message" th:if="${#fields.hasErrors('specialiteId')}" th:errors="*{specialiteId}"></div>
                </div>

                <div class="col-md-6 mb-3">
                    <label class="form-label required-field">Centre d'examen</label>
                    <select th:field="*{centreExamenId}" class="form-select" required id="centreSelect">
                        <option value="">-- Choisir un centre --</option>
                        <option th:each="c : ${centres}" th:value="${c.id}"
                                th:text="${c.code + ' - ' + c.villeNom}"></option>
                    </select>
                    <div class="error-message" th:if="${#fields.hasErrors('centreExamenId')}" th:errors="*{centreExamenId}"></div>
                </div>
            </div>

            <!-- Documents à joindre - SECTION CORRIGÉE -->
            <div class="row mb-4">
                <h5 class="form-section-title">
                    <i class="fas fa-file-upload me-2"></i>Documents à joindre
                </h5>

                <div class="col-md-4 mb-3">
                    <label class="form-label required-field">CV</label>
                    <div class="file-input-wrapper">
                        <!-- SUPPRESSION de th:field pour éviter la duplication -->
                        <input type="file" name="cvFile" class="form-control" required accept="application/pdf" id="cvFile">
                        <div class="file-input-info">
                            <i class="fas fa-info-circle me-1"></i>PDF uniquement, max 10Mo
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <label class="form-label required-field">Copie CIN</label>
                    <div class="file-input-wrapper">
                        <!-- SUPPRESSION de th:field pour éviter la duplication -->
                        <input type="file" name="cinFile" class="form-control" required accept="application/pdf" id="cinFile">
                        <div class="file-input-info">
                            <i class="fas fa-info-circle me-1"></i>PDF uniquement, max 10Mo
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <label class="form-label required-field">Diplôme</label>
                    <div class="file-input-wrapper">
                        <!-- SUPPRESSION de th:field pour éviter la duplication -->
                        <input type="file" name="diplomeFile" class="form-control" required accept="application/pdf" id="diplomeFile">
                        <div class="file-input-info">
                            <i class="fas fa-info-circle me-1"></i>PDF uniquement, max 10Mo
                        </div>
                    </div>
                </div>
            </div>

            <!-- Préférences -->
            <div class="row mb-4">
                <h5 class="form-section-title">
                    <i class="fas fa-cog me-2"></i>Préférences de notification
                </h5>

                <div class="col-md-6 mb-3">
                    <label class="form-label">Type de notifications</label>
                    <select th:field="*{notifications}" class="form-select">
                        <option th:value="EMAIL" th:selected="${candidatureDto.notifications == null}">Email uniquement</option>
                        <option th:value="SMS">SMS uniquement</option>
                        <option th:value="LES_DEUX">Email et SMS</option>
                    </select>
                    <small class="text-muted">Choisissez comment recevoir les notifications sur votre candidature</small>
                </div>

                <div class="col-12 mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" th:field="*{accepter}" id="accepter" required>
                        <label class="form-check-label" for="accepter">
                            <span class="required-field">J'accepte les conditions d'utilisation</span>
                            et je certifie l'exactitude des informations fournies
                            <a href="#" data-bs-toggle="modal" data-bs-target="#conditionsModal" class="ms-2">
                                <i class="fas fa-external-link-alt"></i> Lire les conditions
                            </a>
                        </label>
                        <div class="error-message" th:if="${#fields.hasErrors('accepter')}" th:errors="*{accepter}"></div>
                    </div>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" class="btn btn-mef" id="submitBtn">
                    <i class="fas fa-paper-plane me-2"></i>Soumettre la candidature
                </button>

                <div class="mt-3">
                    <a th:href="@{/public/}" class="btn btn-home me-2">
                        <i class="fas fa-home me-2"></i> Retour à l'accueil
                    </a>
                    <a th:href="@{/public/concours}" class="btn btn-home">
                        <i class="fas fa-trophy me-2"></i> Voir les concours
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Modal Conditions -->
<div class="modal fade" id="conditionsModal" tabindex="-1" aria-labelledby="conditionsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="conditionsModalLabel">
                    <i class="fas fa-file-contract me-2"></i>Conditions d'utilisation
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6><i class="fas fa-check-circle me-2 text-success"></i>1. Acceptation des conditions</h6>
                <p>En utilisant ce service, vous acceptez les présentes conditions d'utilisation et vous engagez à respecter toutes les procédures du concours.</p>

                <h6><i class="fas fa-shield-alt me-2 text-primary"></i>2. Protection des données personnelles</h6>
                <p>Les informations recueillies font l'objet d'un traitement informatique destiné à la gestion des candidatures aux concours administratifs. Conformément à la loi 09-08, vous bénéficiez d'un droit d'accès et de rectification aux informations qui vous concernent.</p>

                <h6><i class="fas fa-exclamation-triangle me-2 text-warning"></i>3. Responsabilité du candidat</h6>
                <p>Le candidat est responsable de l'exactitude des informations fournies. Toute fausse déclaration entraînera l'annulation de la candidature.</p>

                <h6><i class="fas fa-file-alt me-2 text-info"></i>4. Documents requis</h6>
                <p>Tous les documents doivent être au format PDF et d'une taille maximale de 10Mo. Les documents illisibles ou non conformes entraîneront le rejet de la candidature.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal" onclick="acceptConditions()">
                    <i class="fas fa-check me-2"></i>J'ai lu et j'accepte
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Pied de page -->
<footer class="ministere-footer" style="background-color: #2c3e50; color: white; padding: 2rem 0; margin-top: 3rem;">
    <div class="container">
        <div class="row">
            <div class="col-md-6 text-center text-md-start">
                <p class="mb-0">&copy; 2025 Ministère de l'économie et des finances. Tous droits réservés.</p>
            </div>
            <div class="col-md-6 text-center text-md-end">
                <ul class="list-inline mb-0">
                    <li class="list-inline-item"><a href="#" class="text-white text-decoration-none">Mentions légales</a></li>
                    <li class="list-inline-item"><a href="#" class="text-white text-decoration-none">Contact</a></li>
                    <li class="list-inline-item"><a href="#" class="text-white text-decoration-none">Aide</a></li>
                </ul>
            </div>
        </div>
    </div>
</footer>

<!-- JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('candidatureForm');
        const submitBtn = document.getElementById('submitBtn');
        const progressBar = document.getElementById('progressBar');
        const progressBarInner = progressBar.querySelector('.progress-bar');

        // Validation en temps réel
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        inputs.forEach(input => {
            input.addEventListener('change', updateProgress);
            input.addEventListener('input', validateField);
        });

        // Validation des fichiers
        const fileInputs = ['cvFile', 'cinFile', 'diplomeFile'];
        fileInputs.forEach(id => {
            const fileInput = document.getElementById(id);
            if (fileInput) {
                fileInput.addEventListener('change', function() {
                    validateFile(this);
                    updateProgress();
                });
            }
        });

        function validateField(event) {
            const field = event.target;
            const errorDiv = field.parentElement.querySelector('.error-message');

            // Supprimer les anciens messages d'erreur
            if (errorDiv && !errorDiv.textContent.includes('*{')) {
                errorDiv.textContent = '';
            }

            // Validation spécifique par type de champ
            if (field.type === 'email') {
                if (field.value && !isValidEmail(field.value)) {
                    showFieldError(field, 'Format d\'email invalide');
                    return false;
                }
            }

            if (field.name === 'cin') {
                if (field.value && field.value.length < 8) {
                    showFieldError(field, 'CIN doit contenir au moins 8 caractères');
                    return false;
                }
            }

            if (field.type === 'date') {
                if (field.value) {
                    const birthDate = new Date(field.value);
                    const today = new Date();
                    const age = today.getFullYear() - birthDate.getFullYear();

                    if (age < 18) {
                        showFieldError(field, 'Vous devez être âgé d\'au moins 18 ans');
                        return false;
                    }
                    if (age > 65) {
                        showFieldError(field, 'Âge maximum dépassé');
                        return false;
                    }
                }
            }

            // Marquer le champ comme valide
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
            return true;
        }

        function validateFile(fileInput) {
            const file = fileInput.files[0];
            const maxSize = 10 * 1024 * 1024; // 10MB

            if (file) {
                if (file.type !== 'application/pdf') {
                    showFieldError(fileInput, 'Seuls les fichiers PDF sont acceptés');
                    fileInput.value = '';
                    return false;
                }

                if (file.size > maxSize) {
                    showFieldError(fileInput, 'Le fichier dépasse la taille maximale de 10Mo');
                    fileInput.value = '';
                    return false;
                }

                fileInput.classList.remove('is-invalid');
                fileInput.classList.add('is-valid');
            }
            return true;
        }

        function showFieldError(field, message) {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');

            let errorDiv = field.parentElement.querySelector('.error-message');
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                field.parentElement.appendChild(errorDiv);
            }
            errorDiv.textContent = message;
        }

        function isValidEmail(email) {
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        }

        function updateProgress() {
            const totalFields = inputs.length + fileInputs.length;
            let filledFields = 0;

            inputs.forEach(input => {
                if (input.type === 'checkbox') {
                    if (input.checked) filledFields++;
                } else if (input.value.trim()) {
                    filledFields++;
                }
            });

            fileInputs.forEach(id => {
                const fileInput = document.getElementById(id);
                if (fileInput && fileInput.files.length > 0) {
                    filledFields++;
                }
            });

            const progress = (filledFields / totalFields) * 100;
            progressBarInner.style.width = progress + '%';

            if (progress > 0) {
                progressBar.style.display = 'block';
            }
        }

        // Gestion de la soumission du formulaire
        form.addEventListener('submit', function(event) {
            event.preventDefault();

            // Validation finale
            let isValid = true;
            inputs.forEach(input => {
                if (!validateField({target: input})) {
                    isValid = false;
                }
            });

            fileInputs.forEach(id => {
                const fileInput = document.getElementById(id);
                if (fileInput && !fileInput.files.length) {
                    showFieldError(fileInput, 'Ce fichier est obligatoire');
                    isValid = false;
                }
            });

            if (isValid) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Envoi en cours...';

                // Soumettre le formulaire
                form.submit();
            } else {
                // Faire défiler vers la première erreur
                const firstError = form.querySelector('.is-invalid');
                if (firstError) {
                    firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        });

        // Fonction pour accepter les conditions depuis le modal
        window.acceptConditions = function() {
            document.getElementById('accepter').checked = true;
            updateProgress();
        };

        // Mise à jour initiale du progrès
        updateProgress();
    });


    document.addEventListener('DOMContentLoaded', function() {
        const concoursSelect = document.getElementById('concoursSelect');
        const specialiteSelect = document.getElementById('specialiteSelect');
        const centreSelect = document.getElementById('centreSelect');

        // Écouter les changements sur la sélection du concours
        concoursSelect.addEventListener('change', function() {
            const concoursId = this.value;

            if (concoursId) {
                // Charger les spécialités pour ce concours
                fetch(`/public/api/specialites/${concoursId}`)
                    .then(response => response.json())
                    .then(specialites => {
                        specialiteSelect.innerHTML = '<option value="">-- Sélectionner une spécialité --</option>';
                        specialites.forEach(specialite => {
                            const option = document.createElement('option');
                            option.value = specialite.id;
                            option.textContent = specialite.libelle;
                            specialiteSelect.appendChild(option);
                        });
                    })
                    .catch(error => {
                        console.error('Erreur lors du chargement des spécialités:', error);
                    });

                // Charger les centres pour ce concours
                fetch(`/public/api/centres/concours/${concoursId}`)
                    .then(response => response.json())
                    .then(centres => {
                        centreSelect.innerHTML = '<option value="">-- Choisir un centre --</option>';
                        centres.forEach(centre => {
                            const option = document.createElement('option');
                            option.value = centre.id;
                            option.textContent = `${centre.code} - ${centre.villeNom}`;
                            centreSelect.appendChild(option);
                        });
                    })
                    .catch(error => {
                        console.error('Erreur lors du chargement des centres:', error);
                    });
            } else {
                // Réinitialiser les sélections si aucun concours n'est choisi
                specialiteSelect.innerHTML = '<option value="">-- Sélectionner une spécialité --</option>';
                centreSelect.innerHTML = '<option value="">-- Choisir un centre --</option>';
            }
        });

        // Autres fonctions JavaScript existantes...
    });
</script>
</body>
</html>