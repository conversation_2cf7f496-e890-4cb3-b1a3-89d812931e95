package com.concours.repository;

import com.concours.entity.Document;
import com.concours.entity.Candidature;
import com.concours.entity.TypeDocument;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentRepository extends JpaRepository<Document, Long> {
    List<Document> findByCandidature(Candidature candidature);
    Optional<Document> findByCandidatureAndType(Candidature candidature, TypeDocument type);
}