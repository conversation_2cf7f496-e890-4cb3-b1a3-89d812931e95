package com.concours.mapper;

import com.concours.dto.CandidatureDTO;
import com.concours.dto.CandidatureCreateDTO;
import com.concours.entity.Candidat;
import com.concours.entity.Candidature;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {DocumentMapper.class})
public interface CandidatureMapper {

    @Mapping(source = "candidat.nom", target = "candidatNom")
    @Mapping(source = "candidat.prenom", target = "candidatPrenom")
    @Mapping(source = "candidat.cin", target = "candidatCin")
    @Mapping(source = "candidat.email", target = "candidatEmail")
    @Mapping(source = "concours.titre", target = "concoursTitre")
    @Mapping(source = "concours.reference", target = "concoursReference")
    @Mapping(source = "specialite.libelle", target = "specialiteLibelle")
    @Mapping(source = "centreExamen.code", target = "centreCode")
    @Mapping(source = "centreExamen.ville.nom", target = "centreVille")
    @Mapping(source = "utilisateurTraitant.username", target = "utilisateurTraitant")
    CandidatureDTO toDTO(Candidature candidature);

    Candidat toCandidat(CandidatureCreateDTO candidatureDTO);
}