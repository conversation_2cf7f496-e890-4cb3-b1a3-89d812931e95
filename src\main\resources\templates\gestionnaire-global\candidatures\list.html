<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Gestion des candidatures</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        :root {
            --bleu-mef: #0056b3;
            --gris-fond: #f8f9fa;
        }

        body {
            background-color: var(--gris-fond);
        }

        .page-container {
            max-width: 1100px;
            margin: 50px auto;
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        h2 {
            color: var(--bleu-mef);
            text-align: center;
            margin-bottom: 30px;
        }

        .btn-mef {
            background-color: var(--bleu-mef);
            color: white;
            font-weight: 600;
        }

        .btn-mef:hover {
            background-color: #003f80;
        }
    </style>
</head>
<body>
<div class="container page-container">
    <h2><i class="fas fa-folder-open me-2"></i>Gestion des candidatures</h2>

    <form class="row g-3 mb-4" method="get" action="#">
        <div class="col-md-3">
            <select name="concoursId" class="form-select">
                <option value="">Tous les concours</option>
                <option th:each="c : ${concours}" th:value="${c.id}" th:text="${c.titre}" th:selected="${c.id == concoursId}"></option>
            </select>
        </div>
        <div class="col-md-3">
            <select name="specialiteId" class="form-select">
                <option value="">Toutes les spécialités</option>
                <option th:each="s : ${specialites}" th:value="${s.id}" th:text="${s.libelle}" th:selected="${s.id == specialiteId}"></option>
            </select>
        </div>
        <div class="col-md-3">
            <select name="centreId" class="form-select">
                <option value="">Tous les centres</option>
                <option th:each="ce : ${centres}" th:value="${ce.id}" th:text="${ce.ville}" th:selected="${ce.id == centreId}"></option>
            </select>
        </div>
        <div class="col-md-3">
            <input type="text" name="diplome" class="form-control" placeholder="Diplôme" th:value="${diplome}">
        </div>
        <div class="col-md-3">
            <select name="statut" class="form-select">
                <option value="">Tous les statuts</option>
                <option value="EN_ATTENTE">En attente</option>
                <option value="VALIDEE">Validée</option>
                <option value="REJETEE">Rejetée</option>
            </select>
        </div>
        <div class="col-md-12 d-flex justify-content-between">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-filter me-1"></i> Filtrer
            </button>
            <a th:href="@{/gestionnaire-global/dashboard}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Retour au tableau de bord
            </a>
        </div>
    </form>

    <div class="table-responsive">
        <table class="table table-bordered table-hover align-middle">
            <thead class="table-light">
            <tr>
                <th>N°</th>
                <th>Candidat</th>
                <th>Concours</th>
                <th>Spécialité</th>
                <th>Centre</th>
                <th>Diplôme</th>
                <th>Date dépôt</th>
                <th>Statut</th>
                <th>Actions</th>
            </tr>
            </thead>
            <tbody>
            <tr th:each="c : ${candidatures}">
                <td th:text="${c.numero}">CN-12345</td>
                <td th:text="${c.candidat.nom + ' ' + c.candidat.prenom}">Dupont Jean</td>
                <td th:text="${c.concours.titre}">Concours A</td>
                <td th:text="${c.specialite.libelle}">Comptabilité</td>
                <td th:text="${c.centreExamen.ville}">Rabat</td>
                <td th:text="${c.diplome}">BTS Informatique</td>
                <td th:text="${#temporals.format(c.dateDepot, 'dd/MM/yyyy')}"></td>
                <td>
                    <span class="badge bg-secondary" th:classappend="${c.statut == 'VALIDEE'} ? ' bg-success' : (c.statut == 'REJETEE') ? ' bg-danger' : ' bg-warning text-dark'" th:text="${c.statut}">EN_ATTENTE</span>
                </td>
                <td>
                    <a th:href="@{'/gestionnaire-global/candidatures/details/' + ${c.numero}}" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a th:href="@{'/gestionnaire-global/candidatures/edit/' + ${c.numero}}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-edit"></i>
                    </a>
                    <a th:href="@{'/gestionnaire-global/candidatures/delete/' + ${c.numero}}" class="btn btn-sm btn-outline-danger" onclick="return confirm('Supprimer cette candidature ?');">
                        <i class="fas fa-trash"></i>
                    </a>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>