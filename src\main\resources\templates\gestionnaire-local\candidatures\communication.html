<!-- communication.html - Page de communication avec un candidat -->
<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Communication avec candidat - MEF</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --bleu-mef: #0056b3;
            --gris-fond: #f8f9fa;
        }

        body { background-color: var(--gris-fond); }

        .page-container {
            max-width: 900px;
            margin: 30px auto;
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .btn-mef {
            background-color: var(--bleu-mef);
            color: white;
        }

        .btn-mef:hover {
            background-color: #003f80;
        }

        .candidat-info {
            background-color: #f8f9fa;
            border-left: 4px solid var(--bleu-mef);
            padding: 20px;
            margin-bottom: 20px;
        }

        .form-floating textarea {
            min-height: 150px;
        }
    </style>
</head>
<body>
<div class="container page-container">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-envelope me-2 text-primary"></i>Communication avec le candidat</h2>
        <a th:href="@{/gestionnaire-local/candidatures/validation}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Retour
        </a>
    </div>

    <!-- Alertes -->
    <div th:if="${success}" class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <span th:text="${success}"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <div th:if="${error}" class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <span th:text="${error}"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- Informations du candidat -->
    <div class="candidat-info">
        <h5><i class="fas fa-user me-2"></i>Informations du candidat</h5>
        <div class="row">
            <div class="col-md-6">
                <p><strong>Nom :</strong> <span th:text="${candidature.candidat.nom + ' ' + candidature.candidat.prenom}">Nom Prénom</span></p>
                <p><strong>Email :</strong> <span th:text="${candidature.candidat.email}"><EMAIL></span></p>
                <p><strong>N° Candidature :</strong> <span th:text="${candidature.numero}">CAND-20250827-A1B2</span></p>
            </div>
            <div class="col-md-6">
                <p><strong>Concours :</strong> <span th:text="${candidature.concours.titre}">Concours A</span></p>
                <p><strong>Spécialité :</strong> <span th:text="${candidature.specialite.libelle}">Spécialité</span></p>
                <p><strong>Statut :</strong>
                    <span class="badge"
                          th:classappend="${candidature.statut == 'VALIDEE'} ? 'bg-success' :
                                         (candidature.statut == 'REJETEE') ? 'bg-danger' : 'bg-warning'"
                          th:text="${candidature.statut}">EN_ATTENTE</span>
                </p>
            </div>
        </div>
    </div>

    <!-- Formulaire d'envoi de message -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-paper-plane me-2"></i>Envoyer un message</h5>
        </div>
        <div class="card-body">
            <form th:action="@{'/gestionnaire-local/candidatures/envoyer-message/' + ${candidature.numero}}"
                  method="post" th:object="${messageForm}">

                <div class="form-floating mb-3">
                    <input type="text" class="form-control" id="sujet" th:field="*{sujet}"
                           placeholder="Sujet du message" maxlength="200" required>
                    <label for="sujet">Sujet du message *</label>
                    <div class="form-text">Maximum 200 caractères</div>
                    <div th:if="${#fields.hasErrors('sujet')}" class="text-danger">
                        <small th:errors="*{sujet}"></small>
                    </div>
                </div>

                <div class="form-floating mb-3">
                    <textarea class="form-control" id="contenu" th:field="*{contenu}"
                              placeholder="Contenu du message" maxlength="2000" required></textarea>
                    <label for="contenu">Contenu du message *</label>
                    <div class="form-text">Maximum 2000 caractères</div>
                    <div th:if="${#fields.hasErrors('contenu')}" class="text-danger">
                        <small th:errors="*{contenu}"></small>
                    </div>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Information :</strong> Le candidat recevra ce message par email et pourra vous répondre directement.
                    Votre adresse email sera indiquée comme expéditeur.
                </div>

                <div class="d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary" onclick="history.back()">
                        <i class="fas fa-times me-1"></i> Annuler
                    </button>
                    <button type="submit" class="btn btn-mef">
                        <i class="fas fa-paper-plane me-1"></i> Envoyer le message
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modèles de messages prédéfinis -->
    <div class="card mt-4">
        <div class="card-header">
            <h6><i class="fas fa-templates me-2"></i>Modèles de messages</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <button type="button" class="btn btn-outline-primary btn-sm mb-2 w-100"
                            onclick="utiliserModele('Documents manquants', 'Bonjour,\n\nNous avons examiné votre candidature mais certains documents semblent manquants ou illisibles. Pourriez-vous nous faire parvenir :\n\n- [Préciser les documents]\n\nCordialement.')">
                        Documents manquants
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm mb-2 w-100"
                            onclick="utiliserModele('Informations complémentaires', 'Bonjour,\n\nPour traiter votre candidature, nous aurions besoin de quelques informations complémentaires :\n\n- [Préciser les informations]\n\nMerci de nous répondre dans les plus brefs délais.\n\nCordialement.')">
                        Infos complémentaires
                    </button>
                </div>
                <div class="col-md-6">
                    <button type="button" class="btn btn-outline-primary btn-sm mb-2 w-100"
                            onclick="utiliserModele('Convocation entretien', 'Bonjour,\n\nVotre candidature a été retenue pour la phase suivante. Vous êtes convoqué(e) à un entretien le :\n\nDate : [À préciser]\nHeure : [À préciser]\nLieu : [À préciser]\n\nCordialement.')">
                        Convocation entretien
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm mb-2 w-100"
                            onclick="utiliserModele('Statut candidature', 'Bonjour,\n\nNous faisons suite à votre candidature n° ' + $('#candidatureNumero').val() + '.\n\n[Préciser le statut et les prochaines étapes]\n\nCordialement.')">
                        Statut candidature
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<input type="hidden" id="candidatureNumero" th:value="${candidature.numero}">

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
<script>
    function utiliserModele(sujet, contenu) {
        document.getElementById('sujet').value = sujet;
        document.getElementById('contenu').value = contenu;

        // Mettre à jour les labels flottants
        const sujetInput = document.getElementById('sujet');
        const contenuInput = document.getElementById('contenu');

        sujetInput.classList.add('is-valid');
        contenuInput.classList.add('is-valid');

        // Focus sur le champ contenu pour permettre la modification
        contenuInput.focus();
        contenuInput.setSelectionRange(contenu.length, contenu.length);
    }

    // Compteur de caractères
    document.getElementById('sujet').addEventListener('input', function() {
        const remaining = 200 - this.value.length;
        const helpText = this.nextElementSibling.nextElementSibling;
        helpText.textContent = `${remaining} caractères restants`;
        helpText.className = remaining < 20 ? 'form-text text-danger' : 'form-text';
    });

    document.getElementById('contenu').addEventListener('input', function() {
        const remaining = 2000 - this.value.length;
        const helpText = this.nextElementSibling.nextElementSibling;
        helpText.textContent = `${remaining} caractères restants`;
        helpText.className = remaining < 100 ? 'form-text text-danger' : 'form-text';
    });
</script>
</body>
</html>