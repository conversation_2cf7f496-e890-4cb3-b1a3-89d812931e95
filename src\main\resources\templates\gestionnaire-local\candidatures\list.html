<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Gestion des candidatures - MEF</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        :root {
            --bleu-mef: #0056b3;
            --gris-fond: #f8f9fa;
        }

        body {
            background-color: var(--gris-fond);
        }

        .page-container {
            max-width: 1400px;
            margin: 50px auto;
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        h2 {
            color: var(--bleu-mef);
            text-align: center;
            margin-bottom: 30px;
        }

        .btn-mef {
            background-color: var(--bleu-mef);
            color: white;
            font-weight: 600;
        }

        .btn-mef:hover {
            background-color: #003f80;
            color: white;
        }

        .table-responsive {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .table {
            margin-bottom: 0;
        }

        .table thead th {
            background-color: var(--bleu-mef);
            color: white;
            border: none;
            font-weight: 600;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .badge {
            font-size: 0.75rem;
        }

        .filters-card {
            margin-bottom: 20px;
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .filters-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
<div class="container page-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-folder-open me-2"></i>Gestion des candidatures</h2>
        <a th:href="@{/gestionnaire-local/dashboard}" class="btn btn-outline-secondary">
            <i class="fas fa-tachometer-alt me-1"></i> Tableau de bord
        </a>
    </div>

    <!-- Messages d'alerte -->
    <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <span th:text="${success}">Message de succès</span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        <span th:text="${error}">Message d'erreur</span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- Filtres -->
    <div class="card filters-card">
        <div class="card-header">
            <h6 class="mb-0"><i class="fas fa-filter me-2"></i>Filtrer les candidatures</h6>
        </div>
        <div class="card-body">
            <form class="row g-3" method="get" th:action="@{/gestionnaire-local/candidatures/list}">
                <div class="col-md-3">
                    <label class="form-label">Concours</label>
                    <select name="concoursId" class="form-select">
                        <option value="">Tous les concours</option>
                        <option th:each="c : ${concours}"
                                th:value="${c.id}"
                                th:text="${c.titre}"
                                th:selected="${c.id == concoursId}"></option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Spécialité</label>
                    <select name="specialiteId" class="form-select">
                        <option value="">Toutes les spécialités</option>
                        <option th:each="s : ${specialites}"
                                th:value="${s.id}"
                                th:text="${s.libelle}"
                                th:selected="${s.id == specialiteId}"></option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Statut</label>
                    <select name="statut" class="form-select">
                        <option value="">Tous les statuts</option>
                        <option value="EN_ATTENTE" th:selected="${statut == 'EN_ATTENTE'}">En attente</option>
                        <option value="VALIDEE" th:selected="${statut == 'VALIDEE'}">Validée</option>
                        <option value="REJETEE" th:selected="${statut == 'REJETEE'}">Rejetée</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Diplôme</label>
                    <input type="text" name="diplome" class="form-control"
                           placeholder="Filtrer par diplôme" th:value="${diplome}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Taille page</label>
                    <select name="size" class="form-select">
                        <option value="10" th:selected="${pageSize == 10}">10</option>
                        <option value="25" th:selected="${pageSize == 25}">25</option>
                        <option value="50" th:selected="${pageSize == 50}">50</option>
                    </select>
                </div>
                <div class="col-md-12 d-flex justify-content-between">
                    <button type="submit" class="btn btn-mef">
                        <i class="fas fa-filter me-1"></i> Filtrer
                    </button>
                    <a th:href="@{/gestionnaire-local/candidatures/validation}" class="btn btn-outline-success">
                        <i class="fas fa-check-circle me-1"></i> Mode validation
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Résultats -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <span class="text-muted">
            <i class="fas fa-info-circle me-1"></i>
            <span th:text="${totalElements}">0</span> candidature(s) trouvée(s)
        </span>
        <div>
            <a th:href="@{/gestionnaire-local/candidatures/validation}" class="btn btn-success btn-sm">
                <i class="fas fa-check-circle me-1"></i> Validation rapide
            </a>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-hover align-middle">
            <thead>
            <tr>
                <th>N° Candidature</th>
                <th>Candidat</th>
                <th>Concours</th>
                <th>Spécialité</th>
                <th>Centre</th>
                <th>Diplôme</th>
                <th>Date dépôt</th>
                <th>Statut</th>
                <th width="200">Actions</th>
            </tr>
            </thead>
            <tbody>
            <tr th:each="c : ${candidatures}">
                <td>
                    <strong th:text="${c.numero}">CN-12345</strong>
                </td>
                <td>
                    <div>
                        <strong th:text="${c.candidat.nom + ' ' + c.candidat.prenom}">Dupont Jean</strong>
                        <br>
                        <small class="text-muted" th:text="${c.candidat.email}"><EMAIL></small>
                    </div>
                </td>
                <td th:text="${c.concours.titre}">Concours A</td>
                <td th:text="${c.specialite.libelle}">Comptabilité</td>
                <td th:text="${c.centreExamen.ville}">Rabat</td>
                <td th:text="${c.diplome}">BTS Informatique</td>
                <td th:text="${#temporals.format(c.dateDepot, 'dd/MM/yyyy')}">01/01/2025</td>
                <td>
                    <span class="badge"
                          th:classappend="${c.statut == 'VALIDEE'} ? 'bg-success' :
                                         (c.statut == 'REJETEE') ? 'bg-danger' : 'bg-warning text-dark'"
                          th:text="${c.statut}">EN_ATTENTE</span>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <!-- Bouton Détails -->
                        <a th:href="@{'/gestionnaire-local/candidatures/details/' + ${c.numero}}"
                           class="btn btn-sm btn-outline-info" title="Voir les détails">
                            <i class="fas fa-eye"></i>
                        </a>

                        <!-- Bouton Validation/Rejet pour candidatures en attente -->
                        <div th:if="${c.statut == 'EN_ATTENTE'}" class="btn-group" role="group">
                            <form th:action="@{'/gestionnaire-local/candidatures/valider/' + ${c.numero}}"
                                  method="post" class="d-inline">
                                <button type="submit" class="btn btn-sm btn-success"
                                        onclick="return confirm('Valider cette candidature ?')"
                                        title="Valider la candidature">
                                    <i class="fas fa-check"></i>
                                </button>
                            </form>
                            <button type="button" class="btn btn-sm btn-danger"
                                    data-bs-toggle="modal"
                                    th:data-bs-target="'#rejectModal' + ${c.numero}"
                                    title="Rejeter la candidature">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <!-- Bouton Communication -->
                        <a th:href="@{'/gestionnaire-local/candidatures/communication/' + ${c.numero}}"
                           class="btn btn-sm btn-outline-primary" title="Contacter le candidat">
                            <i class="fas fa-envelope"></i>
                        </a>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <!-- Message si aucune candidature -->
    <div th:if="${candidatures.empty}" class="text-center py-5">
        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">Aucune candidature trouvée</h5>
        <p class="text-muted">Aucune candidature ne correspond aux critères de recherche.</p>
        <a th:href="@{/gestionnaire-local/candidatures/list}" class="btn btn-outline-primary">
            <i class="fas fa-refresh me-1"></i> Réinitialiser les filtres
        </a>
    </div>

    <!-- Pagination -->
    <div th:if="${totalPages > 1}" class="d-flex justify-content-center mt-4">
        <nav aria-label="Navigation des pages">
            <ul class="pagination">
                <li class="page-item" th:classappend="${currentPage == 0} ? 'disabled'">
                    <a class="page-link"
                       th:href="@{/gestionnaire-local/candidatures/list(page=${currentPage - 1}, size=${pageSize}, concoursId=${concoursId}, specialiteId=${specialiteId}, statut=${statut}, diplome=${diplome})}">
                        <i class="fas fa-chevron-left"></i> Précédent
                    </a>
                </li>

                <!-- Pages numériques -->
                <li th:each="i : ${#numbers.sequence(0, totalPages - 1)}"
                    class="page-item" th:classappend="${i == currentPage} ? 'active'">
                    <a class="page-link"
                       th:href="@{/gestionnaire-local/candidatures/list(page=${i}, size=${pageSize}, concoursId=${concoursId}, specialiteId=${specialiteId}, statut=${statut}, diplome=${diplome})}"
                       th:text="${i + 1}">1</a>
                </li>

                <li class="page-item" th:classappend="${currentPage == totalPages - 1} ? 'disabled'">
                    <a class="page-link"
                       th:href="@{/gestionnaire-local/candidatures/list(page=${currentPage + 1}, size=${pageSize}, concoursId=${concoursId}, specialiteId=${specialiteId}, statut=${statut}, diplome=${diplome})}">
                        Suivant <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
</div>

<!-- Modales de rejet -->
<div th:each="candidature : ${candidatures}" th:if="${candidature.statut == 'EN_ATTENTE'}">
    <div class="modal fade" th:id="'rejectModal' + ${candidature.numero}" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form th:action="@{'/gestionnaire-local/candidatures/rejeter/' + ${candidature.numero}}" method="post">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-times-circle text-danger me-2"></i>
                            Rejeter la candidature
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Vous êtes sur le point de rejeter la candidature de