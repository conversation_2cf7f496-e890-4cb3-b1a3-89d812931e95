package com.concours.controller;

import com.concours.dto.CandidatureCreateDTO;
import com.concours.dto.CandidatureDTO;
import com.concours.service.CandidatureService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/candidatures")
@CrossOrigin(origins = "*")
public class CandidatureController {

    private final CandidatureService candidatureService;

    public CandidatureController(CandidatureService candidatureService) {
        this.candidatureService = candidatureService;
    }

    @PostMapping
    public ResponseEntity<String> soumettreCandidature(
            @ModelAttribute CandidatureCreateDTO candidatureDTO,
            @RequestParam("cvFile") MultipartFile cvFile,
            @RequestParam("cinFile") MultipartFile cinFile,
            @RequestParam("diplomeFile") MultipartFile diplomeFile) {
        String numero = candidatureService.soumettreCandiature(candidatureDTO, cvFile, cinFile, diplomeFile);
        return ResponseEntity.ok(numero);
    }

    @GetMapping("/{numero}")
    public ResponseEntity<CandidatureDTO> getCandidature(@PathVariable String numero) {
        CandidatureDTO candidature = candidatureService.getCandidatureByNumero(numero);
        return ResponseEntity.ok(candidature);
    }

    @GetMapping("/centre/{centreId}")
    public ResponseEntity<Page<CandidatureDTO>> getCandidaturesByCentre(
            @PathVariable Long centreId, Pageable pageable) {
        Page<CandidatureDTO> candidatures = candidatureService.getCandidaturesByCentre(centreId, pageable);
        return ResponseEntity.ok(candidatures);
    }

    @PutMapping("/{numero}/valider/{utilisateurId}")
    public ResponseEntity<Void> validerCandidature(
            @PathVariable String numero, @PathVariable Long utilisateurId) {
        candidatureService.validerCandidature(numero, utilisateurId);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{numero}/rejeter/{utilisateurId}")
    public ResponseEntity<Void> rejeterCandidature(
            @PathVariable String numero, @PathVariable Long utilisateurId, @RequestParam String motif) {
        candidatureService.rejeterCandidature(numero, utilisateurId, motif);
        return ResponseEntity.ok().build();
    }
}