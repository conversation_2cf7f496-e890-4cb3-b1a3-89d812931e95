<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle}">Accueil - Portail des Concours</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        :root {
            --primary-color: #2c3e50;
            --ministere-blue: #0056b3;
            --light-bg: #f8f9fa;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--light-bg);
        }

        .ministere-header {
            background-color: white;
            padding: 10px 0;
            border-bottom: 1px solid #e0e0e0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .ministere-logo {
            height: 75px;
            margin-right: 15px;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), var(--ministere-blue));
            color: white;
            padding: 5rem 0;
            margin-bottom: 2rem;
        }

        .section-title {
            position: relative;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }

        .section-title::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--ministere-blue);
        }

        .card {
            border: none;
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .ministere-footer {
            background-color: var(--primary-color);
            color: white;
            padding: 3rem 0;
            margin-top: 3rem;
        }

        .badge-status {
            position: absolute;
            top: 15px;
            right: 15px;
        }

        .card-title {
            min-height: 48px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
        }

        .no-concours {
            padding: 3rem;
            text-align: center;
            background-color: #f8f9fa;
            border-radius: 10px;
        }

        .card-text-limited {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</head>

<body>
<!-- En-tête -->
<header class="ministere-header">
    <div class="container d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            <img src="https://imgs.search.brave.com/_sQmz83PoFynMn9wl0RNhXY0yh8yKwDM0fXEtyPgd3Y/rs:fit:860:0:0:0/g:ce/aHR0cHM6Ly91cGxv/YWQud2lraW1lZGlh/Lm9yZy93aWtpcGVk/aWEvY29tbW9ucy9j/L2NiL01FRl9NT1JP/Q0NPLnBuZw"
                 alt="Logo Ministère" class="ministere-logo">
            <div>
                <h4 class="mb-0" style="color: var(--ministere-blue);">Ministère de l'économie et des finances</h4>
                <p class="mb-0 text-muted">Portail des Concours Administratifs</p>
            </div>
        </div>
        <div class="d-none d-md-block">
            <a th:href="@{/public/suivi}" class="btn btn-outline-primary me-2">
                <i class="fas fa-search me-1"></i> Suivi
            </a>
            <a th:href="@{/public/candidature}" class="btn btn-primary me-2">
                <i class="fas fa-file-alt me-1"></i> Postuler
            </a>
            <a th:href="@{/auth/login}" class="btn btn-outline-success">
                <i class="fas fa-sign-in-alt me-1"></i> Connexion
            </a>
        </div>
    </div>
</header>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark" style="background-color: var(--ministere-blue);">
    <div class="container">
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="mainNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link active" th:href="@{/public/}"><i class="fas fa-home me-1"></i> Accueil</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/public/concours}"><i class="fas fa-trophy me-1"></i> Concours</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{#informations}"><i class="fas fa-info-circle me-1"></i> Informations</a>
                </li>
            </ul>
            <form class="d-flex" th:action="@{/public/concours}" method="get">
                <div class="input-group">
                    <input type="text" class="form-control" name="search" placeholder="Rechercher...">
                    <button class="btn btn-light" type="submit"><i class="fas fa-search"></i></button>
                </div>
            </form>
        </div>
    </div>
</nav>

<!-- Contenu principal -->
<main>
    <!-- Section Hero -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 mb-4 fw-bold">
                <i class="fas fa-medal me-3"></i>
                Concours Administratifs 2025
            </h1>
            <p class="lead mb-5">
                Postulez en ligne aux concours de la fonction publique et suivez votre candidature en temps réel
            </p>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <a th:href="@{/public/candidature}" class="btn btn-light btn-lg me-3 px-4 py-3">
                        <i class="fas fa-file-upload me-2"></i>Déposer une candidature
                    </a>
                    <a th:href="@{/public/suivi}" class="btn btn-outline-light btn-lg px-4 py-3">
                        <i class="fas fa-search me-2"></i>Suivre mon dossier
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistiques -->
    <div class="container mb-5">
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center py-4">
                        <i class="fas fa-calendar-check fa-3x mb-3 text-primary"></i>
                        <h3 class="stat-number mb-2" th:text="${stats != null ? stats.nbConcours : '0'}">0</h3>
                        <p class="mb-0 text-muted">Concours ouverts</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center py-4">
                        <i class="fas fa-users fa-3x mb-3 text-success"></i>
                        <h3 class="stat-number mb-2" th:text="${stats != null ? stats.nbCandidatures  : '0'}">0</h3>
                        <p class="mb-0 text-muted">Candidats cette année</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center py-4">
                        <i class="fas fa-briefcase fa-3x mb-3 text-warning"></i>
                        <h3 class="stat-number mb-2" th:text="${stats != null ? stats.nbPostes : '0'}">0</h3>
                        <p class="mb-0 text-muted">Postes à pourvoir</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Concours disponibles -->
    <div class="container mb-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title">
                <i class="fas fa-trophy me-2 text-primary"></i>
                Concours Ouverts
            </h2>
            <a th:href="@{/public/concours}" class="btn btn-outline-primary">
                Voir tous les concours <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>

        <div class="row" th:if="${concours != null and !#lists.isEmpty(concours)}">
            <div th:each="c : ${concours}" class="col-md-4 mb-4">
                <div class="card h-100 position-relative">
                    <span class="badge bg-success badge-status">Ouvert</span>
                    <div class="card-body">
                        <h5 class="card-title" th:text="${c.titre}">Titre du concours</h5>
                        <!-- Affichage des conditions (limitée à 100 caractères) -->
                        <p class="card-text text-muted card-text-limited"
                           th:if="${c.conditions != null and c.conditions.length() > 0}"
                           th:text="${#strings.abbreviate(c.conditions, 100)}">
                            Conditions du concours...
                        </p>
                        <p class="card-text text-muted" th:if="${c.conditions == null or c.conditions.length() == 0}">
                            <i>Aucune information supplémentaire</i>
                        </p>
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-briefcase me-1"></i>
                                Postes: <span th:text="${c.nbPostes}">0</span>
                            </small>
                        </div>
                        <a th:href="@{/public/concours/{id}(id=${c.id})}" class="btn btn-primary">
                            Voir détails
                        </a>
                    </div>
                    <div class="card-footer bg-white">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            Du <span th:text="${#temporals.format(c.dateOuverture, 'dd/MM/yyyy')}">01/01/2025</span>
                            au <span th:text="${#temporals.format(c.dateCloture, 'dd/MM/yyyy')}">31/12/2025</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <div class="row" th:if="${concours == null or #lists.isEmpty(concours)}">
            <div class="col-12">
                <div class="no-concours">
                    <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">Aucun concours ouvert actuellement</h4>
                    <p class="text-muted">Les prochaines sessions de concours seront annoncées prochainement.</p>
                    <a th:href="@{/public/concours}" class="btn btn-outline-primary mt-3">
                        Voir les concours à venir
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Informations -->
    <section id="informations" class="container mb-5">
        <h2 class="section-title">
            <i class="fas fa-info-circle me-2 text-primary"></i>
            Informations Pratiques
        </h2>
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-file-alt me-2"></i>Documents requis</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>CV à jour</li>
                            <li><i class="fas fa-check text-success me-2"></i>Copie de la CIN</li>
                            <li><i class="fas fa-check text-success me-2"></i>Diplômes certifiés</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-phone me-2"></i>Contact</h5>
                        <p><strong><i class="fas fa-envelope me-2"></i>Email:</strong> <EMAIL></p>
                        <p><strong><i class="fas fa-phone me-2"></i>Téléphone:</strong> +212 5 37 67 XX XX</p>
                        <p><strong><i class="fas fa-clock me-2"></i>Horaires:</strong> 8h-16h (Lun-Ven)</p>
                        <p><strong><i class="fas fa-map-marker-alt me-2"></i>Adresse:</strong> Ministère de l'économie et des finances, Rabat</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>

<!-- Pied de page -->
<footer class="ministere-footer">
    <div class="container">
        <div class="row">
            <div class="col-md-6 text-center text-md-start">
                <p class="mb-0">&copy; 2025 Ministère de l'économie et des finances. Tous droits réservés.</p>
            </div>
            <div class="col-md-6 text-center text-md-end">
                <ul class="list-inline mb-0">
                    <li class="list-inline-item"><a href="#" class="text-white">Mentions légales</a></li>
                    <li class="list-inline-item"><a href="#" class="text-white">Contact</a></li>
                    <li class="list-inline-item"><a href="#" class="text-white">Accessibilité</a></li>
                </ul>
            </div>
        </div>
    </div>
</footer>

<!-- JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>