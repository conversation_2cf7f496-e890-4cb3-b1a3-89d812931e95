<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Réinitialiser le mot de passe - Plateforme de Concours - MEF</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <style>
        :root {
            --bleu-mef: #0056b3;
            --vert-mef: #4caf50;
            --gris-fond: #f8f9fa;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--gris-fond) 0%, #e8f5e8 100%);
            min-height: 100vh;
        }

        .reset-container {
            max-width: 500px;
            margin: 80px auto;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .reset-title {
            background: linear-gradient(135deg, var(--vert-mef), #2e7d32);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .btn-mef-success {
            background: linear-gradient(135deg, var(--vert-mef), #2e7d32);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-mef-success:hover {
            background: linear-gradient(135deg, #2e7d32, var(--vert-mef));
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
            color: white;
        }

        .password-strength {
            height: 5px;
            border-radius: 3px;
            margin-top: 5px;
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>

<div class="reset-container">
    <div class="reset-title">
        <h3><i class="fas fa-shield-alt me-2"></i>Nouveau mot de passe</h3>
        <p class="mb-0">Créez un mot de passe sécurisé</p>
    </div>

    <div class="p-4">
        <form th:action="@{/auth/reset-password}" method="post">
            <input type="hidden" name="token" th:value="${token}">

            <!-- Message d'erreur -->
            <div th:if="${error}" class="alert alert-danger mb-3">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span th:text="${error}">Erreur</span>
            </div>

            <!-- Nouveau mot de passe -->
            <div class="mb-3">
                <label for="password" class="form-label fw-semibold">Nouveau mot de passe</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-lock text-muted"></i></span>
                    <input type="password" class="form-control" id="password" name="password"
                           placeholder="Nouveau mot de passe" required minlength="8">
                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <div class="password-strength bg-light" id="passwordStrength"></div>
                <small class="text-muted">Minimum 8 caractères</small>
            </div>

            <!-- Confirmer mot de passe -->
            <div class="mb-4">
                <label for="confirmPassword" class="form-label fw-semibold">Confirmer le mot de passe</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-check text-muted"></i></span>
                    <input type="password" class="form-control" id="confirmPassword" name="confirmPassword"
                           placeholder="Confirmer le mot de passe" required>
                    <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <small id="passwordMatch" class="text-muted"></small>
            </div>

            <!-- Bouton -->
            <div class="d-grid">
                <button type="submit" class="btn btn-mef-success" id="submitBtn" disabled>
                    <i class="fas fa-save me-2"></i> Réinitialiser le mot de passe
                </button>
            </div>
        </form>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function () {
        const input = document.getElementById('password');
        const icon = this.querySelector('i');
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.replace('fa-eye', 'fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.replace('fa-eye-slash', 'fa-eye');
        }
    });

    document.getElementById('toggleConfirmPassword').addEventListener('click', function () {
        const input = document.getElementById('confirmPassword');
        const icon = this.querySelector('i');
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.replace('fa-eye', 'fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.replace('fa-eye-slash', 'fa-eye');
        }
    });

    // Password strength and matching
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirmPassword');
    const strengthBar = document.getElementById('passwordStrength');
    const matchText = document.getElementById('passwordMatch');
    const submitBtn = document.getElementById('submitBtn');

    function checkPasswordStrength(pwd) {
        let strength = 0;
        if (pwd.length >= 8) strength++;
        if (/[a-z]/.test(pwd)) strength++;
        if (/[A-Z]/.test(pwd)) strength++;
        if (/[0-9]/.test(pwd)) strength++;
        if (/[^A-Za-z0-9]/.test(pwd)) strength++;

        const colors = ['#dc3545', '#fd7e14', '#ffc107', '#20c997', '#28a745'];
        const widths = ['20%', '40%', '60%', '80%', '100%'];
        
        strengthBar.style.backgroundColor = colors[strength - 1] || '#e9ecef';
        strengthBar.style.width = widths[strength - 1] || '0%';
        
        return strength;
    }

    function checkPasswordMatch() {
        if (confirmPassword.value === '') {
            matchText.textContent = '';
            matchText.className = 'text-muted';
            return false;
        }
        
        if (password.value === confirmPassword.value) {
            matchText.textContent = '✓ Les mots de passe correspondent';
            matchText.className = 'text-success';
            return true;
        } else {
            matchText.textContent = '✗ Les mots de passe ne correspondent pas';
            matchText.className = 'text-danger';
            return false;
        }
    }

    function updateSubmitButton() {
        const strength = checkPasswordStrength(password.value);
        const match = checkPasswordMatch();
        submitBtn.disabled = !(strength >= 3 && match);
    }

    password.addEventListener('input', updateSubmitButton);
    confirmPassword.addEventListener('input', updateSubmitButton);
</script>
</body>
</html>