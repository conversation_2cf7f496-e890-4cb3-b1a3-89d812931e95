package com.concours.service;

import com.concours.entity.Candidature;
import com.concours.entity.Document;
import com.concours.entity.TypeDocument;
import com.concours.repository.DocumentRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class DocumentService {

    private final DocumentRepository documentRepository;
    private final FileUploadService fileUploadService;

    /**
     * Upload un document pour une candidature
     */
    public Document uploadDocument(Candidature candidature, MultipartFile file, TypeDocument type) throws IOException {
        // Upload du fichier
        String chemin = fileUploadService.uploadFile(file, type.name().toLowerCase());

        // Création et enregistrement du document
        Document document = new Document();
        document.setType(type);
        document.setCheminFichier(chemin);
        document.setCandidature(candidature);

        return documentRepository.save(document);
    }

    /**
     * Récupère tous les documents liés à une candidature
     */
    public List<Document> getDocumentsByCandidature(Candidature candidature) {
        return documentRepository.findByCandidature(candidature);
    }

    /**
     * Récupère un document d'une candidature par son type
     */
    public Optional<Document> getDocumentByCandidatureAndType(Candidature candidature, TypeDocument type) {
        return documentRepository.findByCandidatureAndType(candidature, type);
    }

    /**
     * Supprime tous les documents liés à une candidature
     */
    public void deleteDocumentsByCandidature(Candidature candidature) {
        List<Document> documents = documentRepository.findByCandidature(candidature);
        if (!documents.isEmpty()) {
            documentRepository.deleteAll(documents);
        }
    }
}
