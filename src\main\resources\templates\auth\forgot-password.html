<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Mot de passe oublié - Plateforme de Concours - MEF</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <style>
        :root {
            --bleu-mef: #0056b3;
            --rouge-mef: #d52b1e;
            --vert-mef: #4caf50;
            --or-mef: #ffc107;
            --gris-fond: #f8f9fa;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--gris-fond) 0%, #e3f2fd 100%);
            min-height: 100vh;
        }

        .forgot-container {
            max-width: 500px;
            margin: 80px auto;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .forgot-title {
            background: linear-gradient(135deg, var(--or-mef), #ff8f00);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .forgot-form {
            padding: 2rem;
        }

        .form-control {
            border-radius: 10px;
            padding: 12px 15px;
            border: 2px solid #e3e3e3;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--or-mef);
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
        }

        .input-group-text {
            border-radius: 10px 0 0 10px;
            background-color: var(--gris-fond);
            border: 2px solid #e3e3e3;
            border-right: none;
        }

        .btn-mef-warning {
            background: linear-gradient(135deg, var(--or-mef), #ff8f00);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-mef-warning:hover {
            background: linear-gradient(135deg, #ff8f00, var(--or-mef));
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
            color: white;
        }

        .btn-back {
            background-color: var(--gris-fond);
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-back:hover {
            background-color: #e9ecef;
            transform: translateY(-1px);
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .info-box {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid var(--bleu-mef);
        }
    </style>
</head>
<body>

<div class="forgot-container">
    <div class="forgot-title">
        <h3><i class="fas fa-key me-2"></i>Mot de passe oublié</h3>
        <p class="mb-0">Récupérez l'accès à votre compte</p>
    </div>

    <div class="forgot-form">
        <!-- Message de succès -->
        <div th:if="${param.success}" class="alert alert-success mb-3">
            <i class="fas fa-check-circle me-2"></i>
            Un email de récupération a été envoyé à votre adresse.
        </div>

        <!-- Message d'erreur -->
        <div th:if="${param.error}" class="alert alert-danger mb-3">
            <i class="fas fa-exclamation-circle me-2"></i>
            Aucun compte trouvé avec cette adresse email.
        </div>

        <!-- Informations -->
        <div class="info-box">
            <h6 class="fw-bold mb-2">
                <i class="fas fa-info-circle text-primary me-2"></i>Comment ça marche ?
            </h6>
            <ul class="mb-0 small">
                <li>Saisissez votre adresse email</li>
                <li>Vous recevrez un lien de récupération</li>
                <li>Cliquez sur le lien pour créer un nouveau mot de passe</li>
            </ul>
        </div>

        <form th:action="@{/auth/forgot-password}" method="post">
            <!-- Email -->
            <div class="mb-4">
                <label for="email" class="form-label fw-semibold">Adresse email</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-envelope text-muted"></i></span>
                    <input type="email" class="form-control" id="email" name="email"
                           placeholder="<EMAIL>" required>
                </div>
                <small class="text-muted">
                    <i class="fas fa-shield-alt me-1"></i>
                    Nous ne partagerons jamais votre email
                </small>
            </div>

            <!-- Bouton d'envoi -->
            <div class="d-grid mb-3">
                <button type="submit" class="btn btn-mef-warning">
                    <i class="fas fa-paper-plane me-2"></i> Envoyer le lien de récupération
                </button>
            </div>

            <!-- Boutons de retour -->
            <div class="row">
                <div class="col-6">
                    <a th:href="@{/auth/login}" class="btn btn-back w-100">
                        <i class="fas fa-arrow-left me-2"></i> Connexion
                    </a>
                </div>
                <div class="col-6">
                    <a th:href="@{/public/}" class="btn btn-back w-100">
                        <i class="fas fa-home me-2"></i> Accueil
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // Auto-focus sur le champ email
    document.getElementById('email').focus();
</script>
</body>
</html>