package com.concours.controller;

import com.concours.dto.*;
import com.concours.entity.StatutCandidature;
import com.concours.exception.BusinessException;
import com.concours.service.*;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.security.Principal;
import java.util.List;

@Slf4j
@Controller
@RequestMapping("/gestionnaire-local")
@PreAuthorize("hasAnyRole('ADMIN', 'GESTIONNAIRE_GLOBAL', 'GESTIONNAIRE_LOCAL')")
public class GestionnaireLocalController {

    @Autowired
    private StatistiquesService statistiquesService;

    @Autowired
    private CandidatureService candidatureService;

    @Autowired
    private UtilisateurService utilisateurService;

    @Autowired
    private ConcoursService concoursService;

    @Autowired
    private SpecialiteService specialiteService;

    @Autowired
    private CentreExamenService centreExamenService;

    @GetMapping("/dashboard")
    public String dashboard(Model model, Principal principal) {
        try {
            // Récupérer l'utilisateur connecté et son centre
            String username = principal.getName();
            Long userId = utilisateurService.getUserIdByUsername(username);
            UtilisateurDTO utilisateur = utilisateurService.getUtilisateurById(userId);

            // Vérifier si l'utilisateur a des centres affectés
            if (utilisateur.getCentresAffectes() == null || utilisateur.getCentresAffectes().isEmpty()) {
                model.addAttribute("error", "Aucun centre d'examen associé à votre compte");
                return "gestionnaire-local/dashboard";
            }

            // Prendre le premier centre affecté (un gestionnaire local est généralement affecté à un seul centre)
            Long centreId = utilisateur.getCentresAffectes().get(0).getId();

            // Obtenir les statistiques spécifiques au centre du gestionnaire local
            StatistiquesLocalDTO stats = getStatistiquesLocal(centreId);

            model.addAttribute("stats", stats);
            model.addAttribute("utilisateur", utilisateur);

            return "gestionnaire-local/dashboard";
        } catch (Exception e) {
            log.error("Erreur lors du chargement du dashboard gestionnaire local", e);
            model.addAttribute("error", "Erreur lors du chargement des statistiques");
            return "gestionnaire-local/dashboard";
        }
    }

    @GetMapping("/candidatures/list")
    public String listeCandidatures(Model model,
                                    Principal principal,
                                    @RequestParam(required = false) Long concoursId,
                                    @RequestParam(required = false) Long specialiteId,
                                    @RequestParam(required = false) String statut,
                                    @RequestParam(required = false) String diplome,
                                    @RequestParam(defaultValue = "0") int page,
                                    @RequestParam(defaultValue = "10") int size) {
        try {
            String username = principal.getName();
            Long userId = utilisateurService.getUserIdByUsername(username);
            UtilisateurDTO utilisateur = utilisateurService.getUtilisateurById(userId);

            Long centreId = getCentreIdFromUser(utilisateur);
            if (centreId == null) {
                model.addAttribute("error", "Aucun centre d'examen associé à votre compte");
                return "gestionnaire-local/candidatures/list";
            }

            Pageable pageable = PageRequest.of(page, size, Sort.by("dateDepot").descending());

            // Récupérer les candidatures du centre du gestionnaire local avec filtres
            Page<CandidatureDTO> candidatures = candidatureService.getCandidaturesByCentreWithFilters(
                    centreId,
                    concoursId,
                    specialiteId,
                    statut,
                    diplome,
                    pageable
            );

            model.addAttribute("candidatures", candidatures.getContent());
            model.addAttribute("currentPage", candidatures.getNumber());
            model.addAttribute("totalPages", candidatures.getTotalPages());
            model.addAttribute("totalElements", candidatures.getTotalElements());
            model.addAttribute("pageSize", size);

            // Filtres pour la vue
            model.addAttribute("concours", concoursService.getConcoursActifs());
            model.addAttribute("specialites", specialiteService.listerToutesLesSpecialites());
            model.addAttribute("centres", List.of(centreExamenService.getCentreExamenById(centreId)));

            // Valeurs des filtres actuels
            model.addAttribute("concoursId", concoursId);
            model.addAttribute("specialiteId", specialiteId);
            model.addAttribute("centreId", centreId);
            model.addAttribute("statut", statut);
            model.addAttribute("diplome", diplome);

            return "gestionnaire-local/candidatures/list";
        } catch (Exception e) {
            log.error("Erreur lors du chargement des candidatures", e);
            model.addAttribute("error", "Erreur lors du chargement des candidatures");
            return "gestionnaire-local/candidatures/list";
        }
    }

    @GetMapping("/candidatures/details/{numero}")
    @PreAuthorize("hasAnyRole('ADMIN', 'GESTIONNAIRE_GLOBAL', 'GESTIONNAIRE_LOCAL')")
    public String detailsCandidature(@PathVariable String numero, Model model, Principal principal) {
        try {
            // Vérifier que le gestionnaire local a accès à cette candidature
            String username = principal.getName();
            Long userId = utilisateurService.getUserIdByUsername(username);
            UtilisateurDTO utilisateur = utilisateurService.getUtilisateurById(userId);

            CandidatureDTO candidature = candidatureService.getCandidatureByNumero(numero);

            // Obtenir le centre ID de l'utilisateur
            Long centreId = getCentreIdFromUser(utilisateur);
            if (centreId == null) {
                model.addAttribute("error", "Aucun centre d'examen associé à votre compte");
                return "redirect:/gestionnaire-local/candidatures/list";
            }

            // Vérifier que la candidature appartient au centre du gestionnaire
            if (!candidature.getCentreId().equals(centreId)) {
                model.addAttribute("error", "Vous n'avez pas accès à cette candidature");
                return "redirect:/gestionnaire-local/candidatures/list";
            }

            model.addAttribute("candidature", candidature);
            return "gestionnaire-local/candidatures/details";
        } catch (BusinessException e) {
            log.error("Candidature non trouvée: {}", numero, e);
            return "redirect:/gestionnaire-local/candidatures/list";
        }
    }

    @GetMapping("/candidatures/validation")
    public String validationCandidatures(Model model, Principal principal,
                                         @RequestParam(defaultValue = "EN_ATTENTE") String statutFiltre,
                                         @RequestParam(defaultValue = "0") int page,
                                         @RequestParam(defaultValue = "15") int size) {
        try {
            String username = principal.getName();
            Long userId = utilisateurService.getUserIdByUsername(username);
            UtilisateurDTO utilisateur = utilisateurService.getUtilisateurById(userId);

            Long centreId = getCentreIdFromUser(utilisateur);
            if (centreId == null) {
                model.addAttribute("error", "Aucun centre d'examen associé à votre compte");
                return "gestionnaire-local/candidatures/validation";
            }

            Pageable pageable = PageRequest.of(page, size, Sort.by("dateDepot").ascending());

            // Récupérer uniquement les candidatures en attente ou selon le filtre
            Page<CandidatureDTO> candidatures = candidatureService.getCandidaturesByCentreAndStatut(
                    centreId,
                    StatutCandidature.valueOf(statutFiltre),
                    pageable
            );

            model.addAttribute("candidatures", candidatures.getContent());
            model.addAttribute("currentPage", candidatures.getNumber());
            model.addAttribute("totalPages", candidatures.getTotalPages());
            model.addAttribute("totalElements", candidatures.getTotalElements());
            model.addAttribute("statutFiltre", statutFiltre);

            return "gestionnaire-local/candidatures/validation";
        } catch (Exception e) {
            log.error("Erreur lors du chargement des candidatures à valider", e);
            model.addAttribute("error", "Erreur lors du chargement des candidatures");
            return "gestionnaire-local/candidatures/validation";
        }
    }

    @PostMapping("/candidatures/valider/{numero}")
    @PreAuthorize("hasAnyRole('ADMIN', 'GESTIONNAIRE_GLOBAL', 'GESTIONNAIRE_LOCAL')")
    public String validerCandidature(@PathVariable String numero,
                                     Principal principal,
                                     RedirectAttributes redirectAttributes) {
        try {
            String username = principal.getName();
            Long userId = utilisateurService.getUserIdByUsername(username);
            UtilisateurDTO utilisateur = utilisateurService.getUtilisateurById(userId);

            // Vérifier l'accès à la candidature
            CandidatureDTO candidature = candidatureService.getCandidatureByNumero(numero);
            Long centreId = getCentreIdFromUser(utilisateur);

            if (centreId == null || !candidature.getCentreId().equals(centreId)) {
                redirectAttributes.addFlashAttribute("error", "Vous n'avez pas accès à cette candidature");
                return "redirect:/gestionnaire-local/candidatures/validation";
            }

            candidatureService.validerCandidature(numero, userId);
            redirectAttributes.addFlashAttribute("success",
                    "Candidature " + numero + " validée avec succès !");

        } catch (Exception e) {
            log.error("Erreur lors de la validation de la candidature", e);
            redirectAttributes.addFlashAttribute("error",
                    "Erreur lors de la validation : " + e.getMessage());
        }

        return "redirect:/gestionnaire-local/candidatures/validation";
    }

    @PostMapping("/candidatures/rejeter/{numero}")
    @PreAuthorize("hasAnyRole('ADMIN', 'GESTIONNAIRE_GLOBAL', 'GESTIONNAIRE_LOCAL')")
    public String rejeterCandidature(@PathVariable String numero,
                                     @RequestParam String motif,
                                     Principal principal,
                                     RedirectAttributes redirectAttributes) {
        try {
            String username = principal.getName();
            Long userId = utilisateurService.getUserIdByUsername(username);
            UtilisateurDTO utilisateur = utilisateurService.getUtilisateurById(userId);

            // Vérifier l'accès à la candidature
            CandidatureDTO candidature = candidatureService.getCandidatureByNumero(numero);
            Long centreId = getCentreIdFromUser(utilisateur);

            if (centreId == null || !candidature.getCentreId().equals(centreId)) {
                redirectAttributes.addFlashAttribute("error", "Vous n'avez pas accès à cette candidature");
                return "redirect:/gestionnaire-local/candidatures/validation";
            }

            if (motif == null || motif.trim().isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Le motif de rejet est obligatoire");
                return "redirect:/gestionnaire-local/candidatures/validation";
            }

            candidatureService.rejeterCandidature(numero, userId, motif);
            redirectAttributes.addFlashAttribute("success",
                    "Candidature " + numero + " rejetée avec succès !");

        } catch (Exception e) {
            log.error("Erreur lors du rejet de la candidature", e);
            redirectAttributes.addFlashAttribute("error",
                    "Erreur lors du rejet : " + e.getMessage());
        }

        return "redirect:/gestionnaire-local/candidatures/validation";
    }

    @GetMapping("/candidatures/communication/{numero}")
    @PreAuthorize("hasAnyRole('ADMIN', 'GESTIONNAIRE_GLOBAL', 'GESTIONNAIRE_LOCAL')")
    public String communicationCandidat(@PathVariable String numero, Model model, Principal principal) {
        try {
            String username = principal.getName();
            Long userId = utilisateurService.getUserIdByUsername(username);
            UtilisateurDTO utilisateur = utilisateurService.getUtilisateurById(userId);

            CandidatureDTO candidature = candidatureService.getCandidatureByNumero(numero);
            Long centreId = getCentreIdFromUser(utilisateur);

            if (centreId == null || !candidature.getCentreId().equals(centreId)) {
                model.addAttribute("error", "Vous n'avez pas accès à cette candidature");
                return "redirect:/gestionnaire-local/candidatures/list";
            }

            model.addAttribute("candidature", candidature);
            model.addAttribute("messageForm", new MessageCandidatDTO());

            return "gestionnaire-local/candidatures/communication";
        } catch (Exception e) {
            log.error("Erreur lors du chargement de la communication", e);
            return "redirect:/gestionnaire-local/candidatures/list";
        }
    }

    @GetMapping("/profile")
    public String profile(Model model, Principal principal) {
        try {
            String username = principal.getName();
            Long userId = utilisateurService.getUserIdByUsername(username);
            UtilisateurDTO utilisateur = utilisateurService.getUtilisateurById(userId);
            model.addAttribute("utilisateur", utilisateur);
            return "gestionnaire-local/profile";
        } catch (BusinessException e) {
            log.error("Utilisateur non trouvé", e);
            return "redirect:/login?error=userNotFound";
        }
    }

    // Méthodes privées utilitaires

    /**
     * Récupère l'ID du centre principal d'un utilisateur
     */
    private Long getCentreIdFromUser(UtilisateurDTO utilisateur) {
        if (utilisateur.getCentresAffectes() != null && !utilisateur.getCentresAffectes().isEmpty()) {
            return utilisateur.getCentresAffectes().get(0).getId();
        }
        return null;
    }

    private StatistiquesLocalDTO getStatistiquesLocal(Long centreExamenId) {
        StatistiquesLocalDTO stats = new StatistiquesLocalDTO();

        try {
            // Candidatures dans le centre
            long nbCandidaturesCentre = candidatureService.countCandidaturesByCentre(centreExamenId);
            stats.setNbCandidatsCentre(nbCandidaturesCentre);

            // Spécialités disponibles dans le centre
            int nbSpecialitesCentre = centreExamenService.countSpecialitesByCentre(centreExamenId);
            stats.setNbSpecialitesCentre(nbSpecialitesCentre);

            // Places restantes (capacité - candidatures validées)
            CentreExamenDTO centre = centreExamenService.getCentreExamenById(centreExamenId);
            long candidaturesValidees = candidatureService.countCandidaturesValideesByCentre(centreExamenId);
            long placesRestantes = Math.max(0, centre.getCapacite() - candidaturesValidees);
            stats.setNbPlacesRestantes(placesRestantes);

            // Candidatures en attente de traitement
            long candidaturesEnAttente = candidatureService.countCandidaturesEnAttenteByCentre(centreExamenId);
            stats.setNbCandidaturesEnAttente(candidaturesEnAttente);

        } catch (Exception e) {
            log.warn("Erreur lors du calcul des statistiques locales", e);
            // Valeurs par défaut en cas d'erreur
            stats.setNbCandidatsCentre(0);
            stats.setNbSpecialitesCentre(0);
            stats.setNbPlacesRestantes(0);
            stats.setNbCandidaturesEnAttente(0);
        }

        return stats;
    }
}