<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Profil Utilisateur - MEF</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #1a56db;
            --secondary-color: #046c4e;
            --accent-color: #9061f9;
            --warning-color: #d97706;
            --text-color: #1f2937;
            --light-bg: #f9fafb;
            --border-color: #e5e7eb;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-color);
            margin: 0;
            line-height: 1.6;
            padding: 20px;
        }

        .page-container {
            max-width: 800px;
            margin: 30px auto;
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .welcome-text h2 {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .welcome-text small {
            color: #6b7280;
            font-size: 0.875rem;
        }

        .profile-card {
            background-color: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin-right: 1.5rem;
        }

        .profile-info h4 {
            margin-bottom: 0.25rem;
            font-weight: 600;
        }

        .profile-info p {
            color: #6b7280;
            margin-bottom: 0;
        }

        .form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(26, 86, 219, 0.25);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #1e4bb8;
            border-color: #1e4bb8;
        }

        .alert {
            border-radius: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
<div class="page-container">
    <div class="header">
        <div class="welcome-text">
            <h2><i class="fas fa-user-circle me-2"></i>Mon Profil</h2>
            <small id="currentTime"></small>
        </div>
        <div>
            <a th:href="@{/admin/dashboard}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> Retour
            </a>
            <form th:action="@{/auth/logout}" method="post" class="d-inline">
                <button type="submit" class="btn btn-outline-danger">
                    <i class="fas fa-sign-out-alt me-1"></i> Déconnexion
                </button>
            </form>
        </div>
    </div>

    <!-- Messages d'alerte -->
    <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <span th:text="${success}"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        <span th:text="${error}"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <div class="profile-card">
        <div class="profile-header">
            <div class="profile-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="profile-info">
                <h4 th:text="${utilisateur.username}">Nom d'utilisateur</h4>
                <p th:text="${utilisateur.role}">Rôle</p>
            </div>
        </div>

        <form th:action="@{/admin/profile/update}" method="post" th:object="${utilisateur}">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="username" class="form-label">Nom d'utilisateur *</label>
                    <input type="text" class="form-control" id="username" th:field="*{username}" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">Email *</label>
                    <input type="email" class="form-control" id="email" th:field="*{email}" required>
                </div>
            </div>

            <div class="mb-3">
                <label for="currentPassword" class="form-label">Mot de passe actuel (pour confirmation)</label>
                <input type="password" class="form-control" id="currentPassword" name="currentPassword" required>
                <div class="form-text">Saisissez votre mot de passe actuel pour confirmer les modifications</div>
            </div>

            <div class="mb-3">
                <label for="newPassword" class="form-label">Nouveau mot de passe (optionnel)</label>
                <input type="password" class="form-control" id="newPassword" name="newPassword"
                       placeholder="Laisser vide pour conserver l'actuel">
                <div class="form-text">Minimum 6 caractères</div>
            </div>

            <div class="mb-3">
                <label for="confirmPassword" class="form-label">Confirmer le nouveau mot de passe</label>
                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword">
            </div>

            <div class="nav-buttons">
                <a th:href="@{/admin/dashboard}" class="btn btn-secondary">
                    <i class="fas fa-times me-1"></i> Annuler
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> Enregistrer les modifications
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    function updateTime() {
        const now = new Date();
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        document.getElementById("currentTime").textContent = now.toLocaleDateString('fr-FR', options);
    }
    setInterval(updateTime, 1000);
    updateTime();

    // Validation du formulaire
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (newPassword && newPassword !== confirmPassword) {
                e.preventDefault();
                alert('Les mots de passe ne correspondent pas');
            }
        });
    });
</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>